{"name": "nga-ui", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^6.4.5", "@mui/icons-material": "^5.8.4", "@mui/lab": "^5.0.0-alpha.95", "@mui/material": "^5.10.1", "@mui/styles": "^5.9.3", "@nv2/nv2-pkg-js-shared-components": "^2.43.2", "@nv2/nv2-pkg-js-theme": "^2.5.1", "@reduxjs/toolkit": "^1.8.5", "@ryaneewx/react-chat-widget": "^3.4.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "antd": "^5.16.1", "axios": "^0.27.2", "core-js": "^2.6.12", "craco-sass-resources-loader": "^1.1.0", "dayjs": "^1.11.6", "formik": "^2.4.5", "jest-junit": "^14.0.1", "jest-silent-reporter": "^0.6.0", "js-big-decimal": "^2.0.4", "lodash": "^4.17.21", "material-table": "^2.0.3", "postcss": "^8.4.16", "postcss-loader": "^7.0.1", "prop-types": "^15.8.1", "qs": "^6.11.0", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-dotdotdot": "^1.3.1", "react-dropzone": "^14.2.2", "react-icons": "^4.4.0", "react-image-gallery": "^1.2.9", "react-infinite-scroll-component": "^6.1.0", "react-number-format": "^5.1.1", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-scroll": "^1.8.7", "react-show-more-text": "^1.5.2", "react-simple-maps": "^3.0.0", "react-world-flags": "^1.5.1", "recharts": "^2.1.16", "redux-logger": "^3.0.6", "sass-loader": "^13.0.2", "yup": "^1.3.2"}, "scripts": {"start": "GENERATE_SOURCEMAP=false PORT=3002 craco start", "start:on-windows": "set GENERATE_SOURCEMAP=false && set PORT=3002 && craco start", "build": "craco build", "lint": "eslint --ext .js --ext .jsx src", "stylelint": "stylelint \"**/*.scss\"", "test:local": "jest --maxWorkers=50% --config ./jest.config.js", "test": "jest --maxWorkers=25% --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "react-scripts eject", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@testing-library/user-event": "^14.4.3", "axios-mock-adapter": "^1.21.1", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-imports": "^2.0.0", "eslint": "^8.24.0", "eslint-config-airbnb": "^19.0.4", "history": "^5.3.0", "husky": "^8.0.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.1.2", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.0.3", "jest-websocket-mock": "^2.4.0", "redux-mock-store": "^1.5.4", "stylelint": "^14.13.0", "stylelint-config-standard-scss": "^5.0.0", "stylelint-scss": "^4.3.0", "whatwg-fetch": "^3.6.2"}}