const sassResourcesLoader = require('craco-sass-resources-loader');
// eslint-disable-next-line import/no-extraneous-dependencies
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { whenProd, whenDev } = require('@craco/craco');
const { dependencies } = require('./package.json');

module.exports = {
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: ['node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss', 'src/assets/styles/variables.module.scss'],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {
      // eslint-disable-next-line no-param-reassign
      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'analytics',
          filename: 'remoteEntry.js',
          exposes: {
            './Analytics': './src/AppProvider.jsx',
          },
          ...whenDev(() => ({
            remotes: {
              container: 'container@http://localhost:3003/remoteEntry.js',
            },
          })),
          ...whenProd(() => ({
            remotes: {
              container: 'container@[analyticsContainerUrl]/remoteEntry.js',
            },
          })),
          shared: {
            ...dependencies,
            react: { eager: true, requiredVersion: dependencies.react },
            'react-dom': {
              eager: true,
              requiredVersion: dependencies['react-dom'],
            },
            '@mui/material': {
              eager: true,
              requiredVersion: dependencies['@mui/material'],
            },
            '@mui/styles': { eager: true, requiredVersion: dependencies['@mui/styles'] },
            '@mui/lab': { eager: true },
            '@nv2/nv2-pkg-js-theme': { eager: true },
            '@nv2/nv2-pkg-js-shared-components': { eager: true },
            axios: { eager: true, requiredVersion: dependencies.axios },
            history: { eager: true },
            'prop-types': { eager: true, requiredVersion: dependencies['prop-types'] },
            'react-cookie': { eager: true },
            'react-redux': { eager: true },
            'react-router-dom': { eager: true, requiredVersion: dependencies['react-router-dom'] },
            recharts: { eager: true, requiredVersion: dependencies.recharts },
            'redux-logger': { eager: true },
            '@reduxjs/toolkit': { eager: true },
            antd: { eager: true },
            'react-number-format': { eager: true },
            dayjs: { eager: true },
            'material-table': { eager: true },
            qs: { eager: true },
            'react-perfect-scrollbar': { eager: true },
            'react-simple-maps': { eager: true },
            'react-world-flags': { eager: true },
            'react-dotdotdot': { eager: true },
            lodash: { eager: true },
          },
        }),
      ];

      // eslint-disable-next-line no-param-reassign
      webpackConfig.output = {
        ...webpackConfig.output,
        // for start as container part
        // ...whenDev(() => ({ publicPath: 'auto', clean: true })),
        // for start as independent application
        ...whenDev(() => ({ publicPath: '/', clean: true })),
        ...whenProd(() => ({ publicPath: '/analytics', clean: true })),
      };

      return webpackConfig;
    },
  },
};
