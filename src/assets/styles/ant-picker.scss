.ant-picker {
  height: 40px;
  border: 1px solid $light-color-400;
  padding: 4px 8px;
  border-radius: 4px !important;

  &:hover {
    border-color: $light-color-400;
    cursor: pointer;
  }
}

.ant-picker-focused {
  box-shadow: none;
}

.ant-picker-panels .ant-picker-cell-inner {
  @media (max-width: $small-mobile-width) {
    font-size: 12px;
  }
}

.ant-picker-cell .ant-picker-cell-inner {
  width: 60px;
  height: 40px;
  padding-top: 18px;
  padding-bottom: 18px;
}

.ant-picker-clear {
  display: none;
}

.ant-picker-input {
  input {
    cursor: pointer;
    padding-left: 8px;
  }
}

.ant-picker-month-panel {
  width: 288px;
}

.ant-picker-panels .ant-picker-month-panel {
  @media (max-width: $mobile-width) {
    width: 200px;
  }

  @media (max-width: $small-mobile-width) {
    width: 155px;
  }
}

.ant-picker-suffix {
  order: 1;

  path {
    fill: $dark-color-300;
  }
}

.ant-picker-cell-disabled::before {
  background-color: transparent;
}

.ant-picker-dropdown {
  z-index: 10000;
}
