$table-actions-shadow-left: -10px 0 8px 0 #2d2a810d;
$light-green-color: #f5fffa;
$light-red-color: #fff5f5;

/* Breakpoints
Stylesheet are written with desktop-first media queries
(max-width in media queries)
 */
$small-desktop-width: 1535px;
$tablet-width: 1199px;
$small-tablet-width: 991px;
$mobile-width: 767px;
$small-mobile-width: 575px;

/* stylelint-disable */
:export {
  tealColor500: $teal-color-500;
  tealColor200: $teal-color-200;
  orangeColor400: $orange-color-400;
  brandBlueColor300: #6E6BCF;

  smallDesktopWidth: $small-desktop-width;
  tabletWidth: $tablet-width;
  smallTabletWidth: $small-tablet-width;
  mobileWidth: $mobile-width;
  smallMobileWidth: $small-mobile-width;
}
/* stylelint-enable */
