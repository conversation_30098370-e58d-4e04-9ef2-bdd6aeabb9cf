import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

import { isEmpty, difference } from 'lodash';
import { IconButton, TextField } from '@mui/material';
import { AiOutlineClear, AiOutlineDelete, AiOutlinePlus } from 'react-icons/ai';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';

import getBudgetsAction from 'pages/BudgetList/GetBudgets/actions';
import { HTTPService } from 'core/services';
import Autocomplete from 'shared/Autocomplete';
import Marker from 'shared/Marker';
import ListPreloader from '@nv2/nv2-pkg-js-shared-components/lib/ListPreloader';
import { useBudgetDetailsContext } from 'pages/BudgetDetails/BudgetDetailsContextProvider';
import LightButton from 'shared/LightButton';
import {
  useBudgetValuesContext,
} from 'pages/BudgetDetails/BudgetValues/BudgetValuesContextProvider';
import TrafficValuesCompareButton from 'shared/TrafficValues/TrafficValuesCompareButton';
import useScrollLock from 'core/hooks/useScrollLock';
import PerfectScrollbar from 'react-perfect-scrollbar';
import budgetsComparisonModifier from './constants';

export const defaultBudgetMarkersColors = ['#6E6BCF', '#F3C73C', '#5B8DEF', '#FF9A26', '#00B7C4'];
let budgetsController = HTTPService.getController();

const BudgetsComparison = ({ isComparisonShown }) => {
  const { budgetId } = useBudgetDetailsContext();
  const { budgetsComparisonData, setBudgetsComparisonData } = useBudgetValuesContext();
  const dispatch = useDispatch();
  const budgets = useSelector((state) => state.budgets.data);
  const isBudgetsLoading = useSelector((state) => state.budgets.isLoading);

  const defaultAutocompleteBudgets = budgetsComparisonData.length
    ? budgets.filter(({ id }) => id !== +budgetId)
    : null;
  const [autocompleteBudgets, setAutocompleteBudgets] = useState(defaultAutocompleteBudgets);

  const [isComparisonBudgetsModalOpen, setIsComparisonBudgetsModalOpen] = useState(false);
  const [budgetMarkersColors, setBudgetMarkersColors] = useState(defaultBudgetMarkersColors);
  const [budgetsComparisonModalData, setBudgetsComparisonModalData] = useState(
    budgetsComparisonData,
  );
  const isButtonAddDisabled = !budgetMarkersColors.length
     || budgets.length - 1 < budgetsComparisonModalData.length;
  const isButtonConfirmDisabled = budgetsComparisonModalData.filter(
    ({ value }) => !isEmpty(value)).length <= 1;
  const [compareBudgetsAmount, setCompareBudgetsAmount] = useState(
    budgetsComparisonData.length || null,
  );

  useScrollLock(isComparisonBudgetsModalOpen);

  const removeBudgetMarkersColor = () => {
    const newBudgetMarkersColors = [...budgetMarkersColors];

    newBudgetMarkersColors.shift();
    setBudgetMarkersColors(newBudgetMarkersColors);
  };

  const getBudgets = () => {
    HTTPService.cancelRequest(budgetsController);

    budgetsController = HTTPService.getController();

    return dispatch(getBudgetsAction(budgetsController));
  };

  const openBudgetsComparisonModal = async () => {
    setIsComparisonBudgetsModalOpen(true);

    if (budgetsComparisonData.length) {
      // eslint-disable-next-line no-restricted-syntax
      for (const budgetComparison of budgetsComparisonData) {
        const newAutocompleteBudgets = autocompleteBudgets.filter(
          ({ id }) => id !== budgetComparison.value.id);

        setAutocompleteBudgets(newAutocompleteBudgets);
      }
      setBudgetsComparisonModalData(budgetsComparisonData);
    }

    const budgetsData = await getBudgets();

    if (budgetsComparisonModalData.length) {
      return;
    }

    const newAutocompleteBudgets = budgetsData.filter(
      ({ id }) => id !== +budgetId);

    setBudgetsComparisonModalData([{
      id: Date.now(),
      markerColor: budgetMarkersColors[0],
      value: { id: budgetId, name: budgetsData.find(({ id }) => id === +budgetId)?.name },
      readOnly: true,
    }]);
    setAutocompleteBudgets(newAutocompleteBudgets);

    removeBudgetMarkersColor();
  };

  const cancelGetBudgetsRequest = () => {
    HTTPService.cancelRequest(budgetsController);
  };

  const closeBudgetsComparisonModal = () => {
    setIsComparisonBudgetsModalOpen(false);
    cancelGetBudgetsRequest();
  };

  const resetBudgetsComparison = () => {
    setBudgetsComparisonData([]);
    setBudgetsComparisonModalData([]);
    setBudgetMarkersColors(defaultBudgetMarkersColors);
    setCompareBudgetsAmount(null);
    closeBudgetsComparisonModal();
  };

  const addBudgetRow = () => {
    setBudgetsComparisonModalData((state) => [
      ...state,
      {
        id: Date.now(),
        markerColor: budgetMarkersColors[0],
        value: {},
      },
    ]);

    removeBudgetMarkersColor();
  };

  const removeBudgetRow = (currentId) => {
    const budgetRowIndex = budgetsComparisonModalData.findIndex(({ id }) => id === +currentId);
    const currentRowMarkerColor = budgetsComparisonModalData[budgetRowIndex].markerColor;
    const currentBudgetValue = budgetsComparisonModalData[budgetRowIndex].value;
    const newBudgetsComparisonModalData = [...budgetsComparisonModalData];
    const newAutocompleteBudgets = [...autocompleteBudgets];

    setBudgetMarkersColors((state) => ([
      ...state,
      currentRowMarkerColor,
    ]));
    newAutocompleteBudgets.push(currentBudgetValue);
    setAutocompleteBudgets(newAutocompleteBudgets);

    newBudgetsComparisonModalData.splice(budgetRowIndex, 1);
    setBudgetsComparisonModalData(newBudgetsComparisonModalData);
  };

  const cancelUnappliedBudgetsForComparison = () => {
    const canceledBudgetsComparisonData = difference(
      budgetsComparisonModalData,
      budgetsComparisonData,
    );

    closeBudgetsComparisonModal();

    if (!canceledBudgetsComparisonData.length) {
      return;
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const canceledBudget of canceledBudgetsComparisonData) {
      removeBudgetRow(canceledBudget.id);
    }
    setBudgetsComparisonModalData(budgetsComparisonData);
  };

  const changeBudgetValue = (currentValue, prevValue, currentId) => {
    const budgetRowIndex = budgetsComparisonModalData.findIndex(({ id }) => id === +currentId);
    const newBudgetsComparisonModalData = [...budgetsComparisonModalData];
    const newAutocompleteBudgets = autocompleteBudgets.filter(({ id }) => id !== currentValue.id);

    if (prevValue?.id) {
      newAutocompleteBudgets.push(prevValue);
    }

    newBudgetsComparisonModalData[budgetRowIndex].value = currentValue;
    setAutocompleteBudgets(newAutocompleteBudgets);
    setBudgetsComparisonModalData(newBudgetsComparisonModalData);
  };

  const budgetRow = ({
    rowId, markerColor, budgetValue, readOnly,
  }) => {
    const autocompleteData = autocompleteBudgets?.filter(({ id }) => id !== budgetValue?.id);

    return (
      <div className="budgets-comparison-modal__row" key={rowId}>
        <Autocomplete
          className="budgets-comparison-modal__row-autocomplete"
          data-testid="budgets-comparison-modal__row-autocomplete"
          autoComplete={false}
          options={autocompleteData}
          getOptionLabel={(option) => (option.name ? option.name : '')}
          isOptionEqualToValue={(option, value) => option.name === value.name}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Budget" />}
          value={budgetValue}
          onChange={(e, value) => changeBudgetValue(value, budgetValue, rowId)}
          renderOption={(props, option) => <span {...props} key={option.id}>{option.name}</span>}
          disableClearable
          readOnly={readOnly}
          freeSolo
          forcePopupIcon
        />
        <Marker backgroundColor={markerColor} className="budgets-comparison-modal__row-marker" />
        {!readOnly && (
        <IconButton
          className="budgets-comparison-modal__row-btn-delete"
          onClick={(e) => removeBudgetRow(e.currentTarget.dataset.id)}
          data-id={rowId}
        >
          <AiOutlineDelete size={22} />
        </IconButton>
        )}
      </div>
    );
  };

  const budgetsComparisonModalContent = (
    <>
      <PerfectScrollbar>
        {budgetsComparisonModalData.map(
          ({
            id, markerColor, value, readOnly,
          }) => budgetRow({
            rowId: id, markerColor, budgetValue: value, readOnly,
          }))}
      </PerfectScrollbar>
      <div className="budgets-comparison-modal__btn-wrap">
        <LightButton
          action={addBudgetRow}
          text="Add budget"
          className="budgets-comparison-modal__btn-add"
          startIcon={<AiOutlinePlus />}
          isDisabled={isButtonAddDisabled}
        />
        <LightButton
          action={resetBudgetsComparison}
          text="Reset comparison"
          className="budgets-comparison-modal__btn-reset"
          startIcon={<AiOutlineClear />}
        />
      </div>
    </>
  );

  const compareBudgets = () => {
    setBudgetsComparisonData(budgetsComparisonModalData);
    setCompareBudgetsAmount(budgetsComparisonModalData.filter(
      ({ value }) => !isEmpty(value)).length);

    closeBudgetsComparisonModal();
  };

  const budgetsComparisonStyles = !isComparisonShown ? {
    display: 'none',
  } : {};

  useEffect(() => () => cancelGetBudgetsRequest(), []);

  return (
    <div
      className={budgetsComparisonModifier}
      id={budgetsComparisonModifier}
      data-testid={budgetsComparisonModifier}
      style={budgetsComparisonStyles}
    >
      <TrafficValuesCompareButton
        openTrafficComparisonModal={openBudgetsComparisonModal}
        compareTrafficAmount={compareBudgetsAmount}
      />
      <CustomModal
        isOpen={isComparisonBudgetsModalOpen}
        handleOpen={cancelUnappliedBudgetsForComparison}
        title="Compare budgets"
        dataTestid="budgets-comparison-modal"
        modalClass="budgets-comparison-modal"
        onClickCancel={cancelUnappliedBudgetsForComparison}
        onClickConfirm={compareBudgets}
        buttonDisable={isButtonConfirmDisabled}
        container={() => document.getElementById('budget-values')}
        BackdropProps={{ invisible: true }}
      >
        {!isBudgetsLoading
          ? budgetsComparisonModalContent
          : <ListPreloader rowAmount={2} />}
      </CustomModal>
    </div>
  );
};

BudgetsComparison.propTypes = {
  isComparisonShown: PropTypes.bool,
};

BudgetsComparison.defaultProps = {
  isComparisonShown: true,
};

export default BudgetsComparison;
