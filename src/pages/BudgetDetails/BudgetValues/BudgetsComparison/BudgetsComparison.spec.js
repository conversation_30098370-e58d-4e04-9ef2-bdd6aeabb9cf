import React from 'react';
import {
  act, fireEvent, render, screen, waitFor, within,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import { createMemoryHistory } from 'history';
import MockAdapter from 'axios-mock-adapter';
import { ThemeProvider } from '@mui/material/styles';
import rootReducer from 'core/rootReducer';
import { ngaAxios } from 'core/services/HTTPService';
import Router, { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';

import { getBudgetDetailsPath } from 'core/configs/paths';
import {
  BudgetDetailsContextProvider,
} from 'pages/BudgetDetails/BudgetDetailsContextProvider';
import budgetsUrl from 'pages/BudgetList/GetBudgets/apiUrls';
import mockBudgetsData from 'pages/BudgetList/GetBudgets/mockBudgetsData';
import { AppContextProvider } from 'AppContextProvider';
import {
  BudgetValuesContextProvider,
} from 'pages/BudgetDetails/BudgetValues/BudgetValuesContextProvider';
import * as BudgetValuesContext
  from 'pages/BudgetDetails/BudgetValues/BudgetValuesContextProvider';
import BudgetsComparison from './BudgetsComparison';
import budgetsComparisonModifier from './constants';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

jest.mock('core/hooks/useScrollLock', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => {}),
}));

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

const btnTexts = {
  compare: 'Compare',
  addBudget: 'Add budget',
  confirm: 'Confirm',
  reset: 'Reset comparison',
};
const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('BudgetDetails: BudgetValues: BudgetKPValues: BudgetComparison', () => {
  const mockBudgetId = 1;
  const route = getBudgetDetailsPath(mockBudgetId);
  const history = createMemoryHistory({ initialEntries: [route] });
  const mockSetBudgetsComparisonData = jest.fn();
  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
  });
  const budgetsComparison = (props) => (
    <Provider store={store}>
      <AppContextProvider>
        <BudgetDetailsContextProvider>
          <BudgetValuesContextProvider>
            <HistoryRouter history={history}>
              <ThemeProvider theme={theme(currentTheme)}>
                <BudgetsComparison {...props} />
              </ThemeProvider>
            </HistoryRouter>
          </BudgetValuesContextProvider>
        </BudgetDetailsContextProvider>
      </AppContextProvider>
    </Provider>
  );

  const mockNgaAxios = new MockAdapter(ngaAxios);
  const contextValues = {
    setBudgetsComparisonData: mockSetBudgetsComparisonData,
    budgetsComparisonData: [],
  };
  const mockRequestsForDefaultRender = () => {
    mockNgaAxios.onGet(budgetsUrl).reply(200, mockBudgetsData);
    jest.spyOn(Router, 'useParams').mockReturnValue({ id: mockBudgetId });
    jest.spyOn(BudgetValuesContext, 'useBudgetValuesContext').mockImplementation(() => contextValues);
  };

  beforeEach(() => {
    mockNgaAxios.reset();
    mockRequestsForDefaultRender();
  });

  const openModal = async () => {
    const compareBudgetsBtn = screen.getByText(btnTexts.compare);

    await act(() => fireEvent.click(compareBudgetsBtn));
  };

  const renderBudgetsComparison = async (props) => {
    await act(() => render(budgetsComparison(props)));
  };

  const selectBudgetValue = (index, value) => {
    const budgetAutocomplete = screen.getAllByTestId('budgets-comparison-modal__row-autocomplete')[index];
    const input = within(budgetAutocomplete).getByRole('combobox');
    const mockOptionVal = mockBudgetsData[1].name;

    budgetAutocomplete.focus();
    fireEvent.change(input, { target: { value: value || mockOptionVal } });
    fireEvent.keyDown(budgetAutocomplete, { key: 'ArrowDown' });
    fireEvent.keyDown(budgetAutocomplete, { key: 'Enter' });
  };

  const confirmComparing = async () => {
    const confirmBtn = screen.getByText(btnTexts.confirm);

    await act(() => fireEvent.click(confirmBtn));
  };

  test('should be "Budgets comparison Modal" in the DOM when user click on "Compare" button', async () => {
    await renderBudgetsComparison();
    await openModal();

    const budgetsComparisonModal = screen.getByTestId('budgets-comparison-modal');

    expect(budgetsComparisonModal).toBeInTheDocument();
  });

  test('should budgetsComparison element has style "display: block" by default', async () => {
    await renderBudgetsComparison();

    const budgetsComparisonEl = screen.getByTestId(budgetsComparisonModifier);

    expect(budgetsComparisonEl).toHaveStyle({ display: 'block' });
  });

  test('should budgetsComparison element has style "display: none" if isComparisonShown = false', async () => {
    const testProps = {
      isComparisonShown: false,
    };

    await renderBudgetsComparison(testProps);

    const budgetsComparisonEl = screen.getByTestId(budgetsComparisonModifier);

    expect(budgetsComparisonEl).toHaveStyle({ display: 'none' });
  });

  test('should be call getBudgets action when user click on "Compare" button', async () => {
    await renderBudgetsComparison();
    await openModal();

    expect(mockNgaAxios.history.get.length).toBe(1);
  });

  test('should be "Confirm button" in the modal was disabled when user click on "Compare" button', async () => {
    await renderBudgetsComparison();
    await openModal();

    const confirmBtn = screen.getByText(btnTexts.confirm);

    expect(confirmBtn).toHaveAttribute('disabled');
  });

  test('should be "Add budgets button" in the modal not disabled when user click on "Compare" button', async () => {
    await renderBudgetsComparison();
    await openModal();

    const addBudgetBtn = screen.getByText(btnTexts.addBudget);

    expect(addBudgetBtn).not.toHaveAttribute('disabled');
  });

  test('should be "Add budgets button" in the modal disabled when user add one row in the modal', async () => {
    await renderBudgetsComparison();
    await openModal();

    const addBudgetBtn = screen.getByText(btnTexts.addBudget);

    await act(() => fireEvent.click(addBudgetBtn));

    expect(addBudgetBtn).toHaveAttribute('disabled');
  });

  test('should be "Confirm button" in the modal not disabled when user add one row in the modal and select budget value', async () => {
    await renderBudgetsComparison();
    await openModal();

    const addBudgetBtn = screen.getByText(btnTexts.addBudget);

    await act(() => fireEvent.click(addBudgetBtn));

    selectBudgetValue(1);

    const confirmBtn = screen.getByText(btnTexts.confirm);

    expect(confirmBtn).not.toHaveAttribute('disabled');
  });

  test('should be compared budgets amount = 2 when user add one row in the modal, select budget value and click "Confirm"', async () => {
    await renderBudgetsComparison();
    await openModal();

    const addBudgetBtn = screen.getByText(btnTexts.addBudget);
    const compareBudgetsBtn = screen.getByText(btnTexts.compare);

    await act(() => fireEvent.click(addBudgetBtn));

    selectBudgetValue(1);

    await confirmComparing();

    expect(compareBudgetsBtn).toHaveTextContent('2');
  });

  test('should not add compared budgets amount in button label', async () => {
    await renderBudgetsComparison();
    await openModal();

    const addBudgetBtn = screen.getByText(btnTexts.addBudget);
    const compareBudgetsBtn = screen.getByText(btnTexts.compare);

    await act(() => fireEvent.click(addBudgetBtn));

    selectBudgetValue(1, mockBudgetsData[1].name);

    expect(compareBudgetsBtn).toHaveTextContent(btnTexts.compare);
  });

  describe('Reset comparison', () => {
    const resetComparison = async () => {
      const resetComparisonBtn = screen.getByText(btnTexts.reset);

      await act(() => fireEvent.click(resetComparisonBtn));
    };

    test('should be call "setBudgetComparisonData" mock id user click "Reset comparison" button', async () => {
      await renderBudgetsComparison();
      await openModal();
      await resetComparison();

      expect(mockSetBudgetsComparisonData).toBeCalledWith([]);
    });

    test('should not be comparison modal in the DOM if user click "Reset comparison" button', async () => {
      await renderBudgetsComparison();
      await openModal();

      const budgetsComparisonModal = screen.getByTestId('budgets-comparison-modal');

      await resetComparison();

      await waitFor(() => expect(budgetsComparisonModal).not.toBeInTheDocument());
    });
  });
});
