.budgets-comparison {
  position: absolute;
  right: 60px;
  top: 15px;
  margin: 0 20px 0 auto !important;
  z-index: 1;

  @media (max-width: $small-mobile-width) {
    right: 35px !important;
    top: 60px;
  }
}

.budget-values .custom-modal {
  position: absolute !important;
  left: unset !important;
  bottom: unset !important;
  top: 60px !important;
  right: 63px !important;

  .MuiPaper-root {
    margin: 0 !important;
  }

  @media (max-width: $small-mobile-width) {
    top: 96px !important;
    right: 0 !important;
  }
}

.budgets-comparison-modal {
  box-shadow: $shadow16;
  width: 455px;

  &__row {
    display: flex;
    align-items: center;
    margin-top: 30px;

    &-marker {
      margin-left: 12px;
    }

    &-autocomplete {
      width: 320px;

      .MuiOutlinedInput-input {
        padding-right: 30px !important;
      }

      @media (max-width: $small-mobile-width) {
        width: 197px;
      }
    }

    &-btn-delete {
      margin-left: 10px !important;
    }
  }

  &__btn {
    &-wrap {
      display: flex;
      margin-top: 30px !important;
    }

    &-add {
      margin-right: 20px !important;
      flex-wrap: nowrap !important;
      white-space: nowrap !important;
    }

    &-reset {
      flex-wrap: nowrap !important;
      white-space: nowrap !important;
    }
  }

  .list-preloader__i {
    height: 40px !important;
    margin-top: 30px !important;
  }

  @media (max-width: $small-mobile-width) {
    padding: 20px !important;
    width: 335px;
  }

  .scrollbar-container {
    @media (max-width: $small-tablet-width) {
      max-height: 300px;
    }

    @media (max-width: $small-mobile-width) {
      max-height: 100px;
    }
  }
}
