import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

import BackButton from '@nv2/nv2-pkg-js-shared-components/lib/BackButton';
import MasterBudgetMarker from 'shared/MasterBudgetMarker';
import { paths } from 'core/configs/paths';
import { HTTPService } from 'core/services';
import useDeviceResolution from 'core/hooks/useDeviceResolution';
import BudgetFilters from 'pages/BudgetDetails/BudgetFilters';
import BudgetCalculation from 'pages/BudgetDetails/BudgetCalculation';
import { useBudgetDetailsContext } from 'pages/BudgetDetails/BudgetDetailsContextProvider';
import EntityName from 'shared/EntityParameters/EntityName';
import EntityOperators from 'shared/EntityParameters/EntityOperators';
import EntityTimeline from 'shared/EntityTimeline';
import EntityParametersPreloader from 'shared/EntityParameters/EntityParametersPreloader';
import BudgetParametersEdit from 'pages/BudgetDetails/BudgetParameters/BudgetParametersEdit';
import BudgetDeleting from 'pages/BudgetDetails/BudgetParameters/BudgetDeleting';
import ParameterItem from 'shared/ParameterItem';
import BudgetTypePreview from 'shared/BudgetType/BudgetTypePreview';
import BudgetDescription from 'shared/BudgetDescription';
import getBudgetParametersAction from './GetBudgetParameters/actions';
import BudgetParametersInfo from './BudgetParametersInfo';

const BudgetParameters = ({ setErrorPageNotFound }) => {
  const { budgetId } = useBudgetDetailsContext();
  const { isSmallDesktop, isTablet, isMobile } = useDeviceResolution();
  const isMediumScreen = isSmallDesktop && !isTablet;
  const descriptionClamp = isMediumScreen ? 1 : 2;

  const dispatch = useDispatch();
  const budgetParameters = useSelector((state) => state.budgetParameters.data);
  const isBudgetParametersLoading = useSelector((state) => state.budgetParameters.isLoading);
  const isMasterBudget = budgetParameters?.is_master;

  const cancelGetBudgetParametersRequest = (controller) => {
    HTTPService.cancelRequest(controller);
  };

  const cancelRequests = (controller) => {
    cancelGetBudgetParametersRequest(controller);
  };

  const getBudgetParameters = async (controller) => {
    try {
      await dispatch(getBudgetParametersAction(controller, budgetId));
    } catch (error) {
      if (error?.response?.status === 404) {
        setErrorPageNotFound(true);
      }
    }
  };

  const actionsRender = () => (
    <div className="budget-parameters__actions">
      {!isMasterBudget && <BudgetParametersEdit getBudgetParameters={getBudgetParameters} />}
      <BudgetFilters />
      {!isMasterBudget && (
        <BudgetDeleting
          budgetId={budgetId}
          budgetListPath={paths.budgetList}
        />
      )}
      <BudgetCalculation />
    </div>
  );

  const budgetParametersRender = () => {
    const {
      // eslint-disable-next-line camelcase
      name, home_operators, start_date, end_date, last_historical_month,
      type, description,
    } = budgetParameters;

    return (
      <>
        <div className="budget-parameters__actions-wrap">
          <BackButton path={paths.budgetList} />
          {isMobile && actionsRender()}
        </div>
        <div className="budget-parameters__title-wrap">
          <EntityName name={name} />
          <BudgetDescription description={description} clamp={descriptionClamp} />
        </div>
        {budgetParameters.is_master && <MasterBudgetMarker />}
        <BudgetParametersInfo />
        <div className="budget-parameters__entities">
          {/* eslint-disable camelcase */}
          <EntityOperators operators={home_operators} />
          <div className="budget-parameters__entities-timeline">
            <EntityTimeline
              startDate={start_date}
              endDate={end_date}
              lastHistoricalMonth={last_historical_month}
            />
          </div>
          <ParameterItem
            label="Type"
            value={(
              <BudgetTypePreview
                budgetTypeValue={type}
                tooltipPlacement="right"
                modifier="parameter-item__value"
              />
            )}
          />
        </div>
        {!isMobile && actionsRender()}
      </>
    );
  };

  useEffect(() => {
    const budgetsController = HTTPService.getController();

    getBudgetParameters(budgetsController);

    return () => cancelRequests(budgetsController);
  }, []);

  return (
    <div className="budget-parameters">
      {isBudgetParametersLoading ? <EntityParametersPreloader /> : budgetParametersRender()}
    </div>
  );
};

BudgetParameters.propTypes = {
  setErrorPageNotFound: PropTypes.func,
};
BudgetParameters.defaultProps = {
  setErrorPageNotFound: () => {},
};

export default BudgetParameters;
