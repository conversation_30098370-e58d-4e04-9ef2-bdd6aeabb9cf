.budget-parameters {
  display: flex;
  align-items: center;
  height: 70px;
  background-color: $white-color;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;

  @media (max-width: $tablet-width) {
    height: auto;
    flex-wrap: wrap;
    row-gap: 20px;
  }

  &__entities {
    display: flex;
    align-items: center;
    margin-right: 20px;

    @media (max-width: $tablet-width) {
      width: 100%;
      gap: 20px;
      margin-right: 0;
    }

    @media (max-width: $small-mobile-width) {
      flex-wrap: wrap;
    }

    &-timeline {
      width: 240px;
    }
  }

  &__title-wrap {
    display: flex;
    flex-direction: column;

    @media (max-width: $tablet-width) {
      max-width: 50%;
    }

    @media (max-width: $mobile-width) {
      max-width: 75%;
    }

    @media (max-width: $small-mobile-width) {
      max-width: 85%;
    }
  }

  .back-button {
    margin-right: 12px !important;

    @media (max-width: $small-mobile-width) {
      width: 100%;
    }
  }

  .master-budget-marker {
    margin-right: 20px;
  }

  &__actions-wrap {
    @media (max-width: $mobile-width) {
      display: flex;
      width: 100%;
    }
  }

  &__actions {
    margin-left: auto;
    display: flex;
    gap: 20px;

    @media (max-width: $tablet-width) {
      position: absolute;
      top: 15px;
      right: 15px;
    }

    @media (max-width: $mobile-width) {
      position: relative;
      top: 0;
      right: 0;
    }
  }

  .entity-parameters-operators {
    margin-right: 40px !important;
    margin-left: 40px !important;

    @media (max-width: $small-desktop-width) {
      margin-right: 30px !important;
      margin-left: 30px !important;
    }

    @media (max-width: $tablet-width) {
      margin: 0 !important;
    }
  }

  .entity-parameters-name {
    @media (max-width: $small-mobile-width) {
      max-width: 100%;
    }
  }

  .parameter-item {
    margin-left: 25px;

    @media (max-width: $small-mobile-width) {
      margin-left: 0;
    }

    &__value {
      text-transform: capitalize !important;
    }
  }
}
