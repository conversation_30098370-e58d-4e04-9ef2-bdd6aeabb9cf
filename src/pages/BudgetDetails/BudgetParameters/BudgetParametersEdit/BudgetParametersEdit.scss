.budget-parameters-edit {
  &__open-btn {
    min-width: 40px !important;
  }

  &-modal {
    .custom-modal {
      &__content {
        width: 450px;
        padding: 20px 0 5px !important;

        @media (max-width: $small-mobile-width) {
          width: 305px;
        }

        @media (max-width: $small-mobile-width) {
          width: auto;
        }
      }

      &__wrap {
        @media (max-width: $small-mobile-width) {
          padding: 20px !important;
        }
      }

      &__title-wrap {
        @media (max-width: $small-mobile-width) {
          gap: 25px !important;
        }
      }
    }

    &__name-wrap {
      align-items: normal;
      margin: 15px 0 25px !important;
      width: 100%;

      .MuiFormLabel-root {
        transform: translate(12px, 12px) scale(1);
        background-color: $white-color;
        padding: 0 5px;
      }

      .MuiOutlinedInput-input {
        color: $dark-color-300 !important;
        min-height: 47px !important;
      }
    }

    &__type {
      width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: 25px !important;
    }

    &__last-historical-month {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 0 0 25px !important;

      &_label {
        width: 100%;
        display: block !important;
        color: $dark-color-300;
        font-weight: bold !important;
      }

      .month-date-picker {
        width: 133px;
      }
    }

    &__description-wrap {
      align-items: normal;
      width: 100%;

      .MuiFormLabel-root {
        transform: translate(12px, 12px) scale(1);
        background-color: $white-color;
        padding: 0 5px;
      }

      .MuiOutlinedInput-input {
        color: $dark-color-300 !important;
        min-height: 47px !important;
        width: 100%;
      }
    }
  }
}
