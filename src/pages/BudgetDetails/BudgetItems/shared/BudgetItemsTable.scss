@import "src/pages/BudgetDetails/BudgetItems/variables";

$above-small-mobile-width: 660px;
$fixed-width: 1735px;

.budget-items-table {
  &__wrap {
    .interactions {
      @media (max-width: $above-small-mobile-width) {
        padding: 15px !important;
      }

      &__actions-selections {
        @media (max-width: $above-small-mobile-width) {
          position: absolute;
          top: 25px;
          right: 15px;
          white-space: nowrap;
        }

        @media (max-width: $small-mobile-width) {
          top: 75px;
          left: 15px;
        }
      }
    }

    .search {
      @media (max-width: $small-mobile-width) {
        width: 205px !important;
      }
    }
  }

  &__cell {
    min-height: $min-table-cell-height !important;

    .home-operators-autocomplete {
      width: 300px;
    }

    .operators-autocomplete, .countries-autocomplete {
      width: 300px;
    }
  }

  &__row {
    &_edit-line {
      .budget-items-table__cell {
        background-color: var(--row-edit-background-color) !important;
      }
    }

    &_edit-regular-line {
      opacity: 0.2;
    }

    &-action-button {
      opacity: 0;

      & svg path {
        stroke: $dark-color-300;
      }
    }

    &:hover {
      .budget-items-table__row-action-button {
        opacity: 1;
      }
    }
  }

  &__sticky-last-column {
    .MuiDataGrid-columnHeaders {
      width: $fixed-width;
      overflow: visible !important;
      position: sticky !important;
      top: 0 !important;
      z-index: 13 !important;
    }

    .MuiDataGrid-main .MuiDataGrid-virtualScroller {
      overflow: visible !important;
    }

    .budget-items-table__header {
      &:last-child {
        position: sticky !important;
        right: 0;
        box-shadow: $table-actions-shadow-left;
        z-index: 12;
      }
    }

    .budget-items-table__row {
      .budget-items-table__cell {
        &:last-child {
          position: sticky;
          right: 0;
          box-shadow: $table-actions-shadow-left;
        }
      }
    }
  }
}
