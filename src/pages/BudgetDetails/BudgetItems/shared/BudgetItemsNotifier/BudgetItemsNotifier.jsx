import React from 'react';
import PropTypes from 'prop-types';

import { AiOutlinePlayCircle } from 'react-icons/ai';

import BudgetComponentBackgroundJobNotifier
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/BudgetComponentBackgroundJobNotifier';
import BudgetComponentNotifier
  from 'features/BudgetDetailsChannel/shared/BudgetComponentNotifier';
import BudgetComponentStorageNotifier
  from 'features/BudgetItems/BudgetComponentStorageNotifier';
import {
  notifierTextsByBudgetType,
  sessionStorageNotifierKeysByBudgetType,
} from 'features/BudgetItems/constants';

const BudgetItemsNotifier = ({
  isBudgetCalculationInProcess,
  isActiveBackgroundJob,
  budgetComponent,
  isNotAvailableByBudgetType,
}) => (
  <div className="budget-notifier__wrap">
    {isActiveBackgroundJob
        && (
        <BudgetComponentBackgroundJobNotifier
          isActiveBackgroundJob={isActiveBackgroundJob}
          budgetComponent={budgetComponent}
        />
        )}
    {isBudgetCalculationInProcess && (
    <BudgetComponentNotifier
      isVisible={isBudgetCalculationInProcess}
      jobType="budget calculation"
      Icon={AiOutlinePlayCircle}
      budgetComponent={budgetComponent}
    />
    )}
    {isNotAvailableByBudgetType && (
    <BudgetComponentStorageNotifier
      isDisabledActions={isNotAvailableByBudgetType}
      notifierText={notifierTextsByBudgetType[budgetComponent]}
      sessionStorageNotifierKey={sessionStorageNotifierKeysByBudgetType[budgetComponent]}
    />
    )}
  </div>
);

BudgetItemsNotifier.propTypes = {
  isBudgetCalculationInProcess: PropTypes.bool,
  isActiveBackgroundJob: PropTypes.bool,
  isNotAvailableByBudgetType: PropTypes.bool,
  budgetComponent: PropTypes.string.isRequired,
};

BudgetItemsNotifier.defaultProps = {
  isBudgetCalculationInProcess: false,
  isNotAvailableByBudgetType: false,
  isActiveBackgroundJob: false,
};

export default BudgetItemsNotifier;
