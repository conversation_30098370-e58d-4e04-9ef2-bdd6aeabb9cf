$less-extra-small-mobile-width: 375px;

.modal-to-copy-budget-components-from-another-budget {
  min-width: 100px;
  width: calc(100vw - 160px);
  max-width: 1536px;
  min-height: 800px;
  padding: 0 !important;

  @media (max-width: $mobile-width) {
    width: unset;
    min-height: 620px;
    max-height: 100%;
    height: unset;
  }

  @media (max-width: $small-mobile-width) {
    min-height: unset;
  }

  &__content {
    display: flex;
    flex-direction: column;
    width: calc(100% - 350px);
    padding: 25px 30px 30px;
    justify-content: space-between;
    min-height: 800px;
    height: 100%;

    @media (max-width: $tablet-width) {
      width: 100%;
    }

    @media (max-width: $mobile-width) {
      width: 100%;
      padding: 20px;
      min-height: 620px;
    }
  }

  .MuiDialogContent-root {
    height: 100% !important;
    display: flex;
  }

  .custom-modal__close-btn {
    position: absolute;
    top: 20px;
    right: 30px;

    @media (max-width: $mobile-width) {
      right: 20px;
    }
  }

  &__budgets-section {
    width: 344px;
    background-color: $light-color-100;
    transition: visibility 0.3s linear, opacity 0.3s linear;
    display: flex;
    flex-direction: column;

    @media (max-width: $tablet-width) {
      position: absolute;
      opacity: 0;
      z-index: 10;
      visibility: hidden;
      height: 100%;
    }

    @media (max-width: $less-extra-small-mobile-width) {
      position: fixed;
    }
  }

  &__filter-btn {
    position: absolute !important;
    top: 60px;
    left: 425px;

    @media (max-width: $small-mobile-width) {
      left: unset;
      right: 20px;
      top: 127px;
    }
  }

  &__wrap {
    .MuiDialog-paperScrollPaper {
      @media (max-width: $small-mobile-width) {
        overflow-y: auto !important;
        min-height: 570px !important;
      }
    }
  }
}
