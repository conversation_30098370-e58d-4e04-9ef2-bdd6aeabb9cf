import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { GrPrevious } from 'react-icons/gr';

import { Icon<PERSON>utton, TextField, Typography } from '@mui/material';

import { HTTPService } from 'core/services';
import getBudgetsAction from 'pages/BudgetList/GetBudgets/actions';
import Autocomplete from 'shared/Autocomplete';
import useDeviceResolution from 'core/hooks/useDeviceResolution';
import { budgetSelectionAutocompleteModifier, budgetSelectionModifier, budgetSelectionTitleModifier } from './constants';
import './BudgetSelection.scss';

const BudgetSelection = ({
  setBudgetValue,
  budgetValue,
  isModalToCopyBudgetComponentsFromAnotherBudgetOpen,
  toggleVisibilityFiltersSection,
  isTitleVisible,
}) => {
  const budgetsData = useSelector((state) => state.budgets.data);

  const { isTablet } = useDeviceResolution();

  const dispatch = useDispatch();
  const getBudgets = (controller) => {
    dispatch(getBudgetsAction(controller));
  };

  const cancelGetBudgetsRequest = (controller) => {
    HTTPService.cancelRequest(controller);
  };

  const changeBudgetValue = (value) => {
    setBudgetValue(value?.id);
  };

  const init = () => {
    const initialBudgetValueId = budgetValue || budgetsData?.[0]?.id;

    setBudgetValue(initialBudgetValueId);
  };

  useEffect(() => {
    init();

    return () => setBudgetValue(null);
  }, [budgetsData]);

  useEffect(() => {
    const getBudgetsController = HTTPService.getController();

    if (isModalToCopyBudgetComponentsFromAnotherBudgetOpen && !budgetsData?.length) {
      getBudgets(getBudgetsController);
    }

    return () => cancelGetBudgetsRequest(getBudgetsController);
  }, [isModalToCopyBudgetComponentsFromAnotherBudgetOpen]);

  return (
    <div className={budgetSelectionModifier}>
      {isTitleVisible && (
        <div className={`${budgetSelectionTitleModifier}__wrap`}>
          <Typography
            variant="h3"
            component="h3"
            className={budgetSelectionTitleModifier}
          >
            Budget
          </Typography>
          {isTablet && (
            <IconButton onClick={toggleVisibilityFiltersSection}>
              <GrPrevious size={20} />
            </IconButton>
          )}
        </div>
      )}
      <Autocomplete
        className={budgetSelectionAutocompleteModifier}
        data-testid={budgetSelectionAutocompleteModifier}
        autoComplete={false}
        options={budgetsData}
        getOptionLabel={(option) => option.name || ''}
        isOptionEqualToValue={(option, value) => option.id === value?.id}
        renderInput={(params) => <TextField {...params} variant="outlined" label="Select Budget" placeholder="Search" />}
        value={budgetsData.find((budget) => budget.id === budgetValue) || null}
        onChange={(_, value) => changeBudgetValue(value)}
        renderOption={(props, option) => <span {...props} key={option.id}>{option.name}</span>}
        disableClearable
      />
    </div>
  );
};

BudgetSelection.propTypes = {
  setBudgetValue: PropTypes.func,
  budgetValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  isModalToCopyBudgetComponentsFromAnotherBudgetOpen: PropTypes.bool,
  toggleVisibilityFiltersSection: PropTypes.func,
  isTitleVisible: PropTypes.bool,
};

BudgetSelection.defaultProps = {
  setBudgetValue: () => {},
  budgetValue: '',
  isModalToCopyBudgetComponentsFromAnotherBudgetOpen: false,
  toggleVisibilityFiltersSection: () => {},
  isTitleVisible: true,
};

export default BudgetSelection;
