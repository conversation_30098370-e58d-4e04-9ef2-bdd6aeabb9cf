$less-extra-small-mobile-width: 375px;

.modal-to-change-agreements-status {
  width: calc(100vw - 160px);
  padding: 0 !important;
  min-height: 800px;
  max-width: 1536px;

  @media (max-width: $mobile-width) {
    width: unset;
    min-height: 620px;
    max-height: 100%;
    height: unset;
  }

  @media (max-width: $small-mobile-width) {
    min-height: unset;
  }

  &__content {
    display: flex;
    flex-direction: column;
    width: calc(100% - 350px);
    padding: 25px 30px 30px;
    justify-content: space-between;
    min-height: 800px;
    height: 100%;
    position: relative;

    @media (max-width: $tablet-width) {
      width: 100%;
    }

    @media (max-width: $mobile-width) {
      width: 100%;
      padding: 20px;
      min-height: 620px;
    }
  }

  .MuiDialogContent-root {
    height: 100% !important;
    display: flex;
  }

  .custom-modal__close-btn {
    position: absolute;
    top: 20px;
    right: 30px;
    z-index: 10;

    @media (max-width: $mobile-width) {
      right: 20px;
      top: 17px;
    }
  }

  &__filters-section {
    width: 344px;
    background-color: $light-color-100;
    transition: visibility 0.3s linear, opacity 0.3s linear;
    display: flex;
    flex-direction: column;
    position: relative;

    @media (max-width: $tablet-width) {
      position: absolute;
      opacity: 0;
      z-index: 10;
      visibility: hidden;
      height: 100%;
    }

    @media (max-width: $less-extra-small-mobile-width) {
      position: fixed;
    }
  }

  &__filter-btn {
    position: relative !important;
    top: 80px;
    left: 390px;
    width: fit-content;

    @media (max-width: $small-mobile-width) {
      position: absolute !important;
      left: unset;
      right: 20px;
      top: 237px;
    }
  }

  &__target-status-selection {
    display: flex;
    flex-direction: column;
    gap: 25px;

    .agreement-statuses-autocomplete {
      width: 250px;
      margin-bottom: 25px;
    }

    &_title {
      @media (max-width: $mobile-width) {
        font-size: 18px !important;
      }
    }
  }

  &__toggle-filters-visibility-btn {
    position: absolute !important;
    top: 10px;
    right: 20px;
  }

  &__wrap {
    .MuiDialog-paperScrollPaper {
      @media (max-width: $small-mobile-width) {
        overflow-y: auto !important;
        min-height: 570px !important;
      }
    }
  }

  .agreements-for-actions-filters__title {
    padding-top: 20px !important;
  }
}
