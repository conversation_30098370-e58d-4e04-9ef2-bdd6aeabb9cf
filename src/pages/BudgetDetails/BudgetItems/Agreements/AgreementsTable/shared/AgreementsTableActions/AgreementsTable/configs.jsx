import {
  AiOutlineLineChart,
} from 'react-icons/ai';
import React from 'react';

import AgreementStatusLabel from 'shared/AgreementStatusLabel';
import FormattedOperators from 'shared/FormattedOperators';
import AgreementActivationStatusIcon from 'shared/AgreementActivationStatusIcon';
import { getFormattedDate } from 'core/utilities/getFormattedDate';
import CalculationStatus from 'shared/CalculationStatus';
import {
  agreementFields,
} from './constants';
import {
  getPeriodForPreview,
} from './utilities';

export const noDataConfig = (primaryColor) => ({
  icon: <AiOutlineLineChart size={24} color={primaryColor} />,
  title: 'No agreements in the budget yet',
  description: 'You can import or add rules using action icons.',
});

const baseAgreementColumns = [
  {
    headerName: 'ID',
    field: agreementFields.id,
    minWidth: 60,
    renderCell: ({ row }) => row.id,
  },
  {
    headerName: 'Agreement reference',
    field: agreementFields.name,
    minWidth: 200,
    renderCell: ({ row }) => row.name,
  },
  {
    headerName: 'Home Operators',
    field: agreementFields.homeOperators,
    minWidth: 120,
    renderCell: ({ row }) => (
      <FormattedOperators data={row.home_operators} keyName="pmn_code" />
    ),
  },
  {
    headerName: 'Partner Operators',
    field: agreementFields.partnerOperators,
    minWidth: 120,
    renderCell: ({ row }) => (
      <FormattedOperators data={row.partner_operators} keyName="pmn_code" />
    ),
  },
  {
    headerName: 'Partner Countries',
    field: agreementFields.partnerCountries,
    minWidth: 150,
    renderCell: ({ row }) => (
      <FormattedOperators data={row.partner_countries} keyName="name" />
    ),
  },
  {
    headerName: 'Period',
    field: 'period',
    minWidth: 100,
    renderCell: ({ row }) => getPeriodForPreview(row.start_date, row.end_date),
  },
  {
    headerName: 'Status',
    field: agreementFields.status,
    minWidth: 65,
    renderCell: ({ row }) => (
      <AgreementStatusLabel statusValue={row.status} />
    ),
  },
  {
    headerName: 'Active',
    field: agreementFields.isActive,
    width: 70,
    renderCell: ({ row }) => (
      <AgreementActivationStatusIcon isActive={row.is_active} />
    ),
  },
];

const compareColumns = [
  {
    headerName: 'Negotiator',
    field: 'negotiator',
    minWidth: 120,
    renderCell: ({ row }) => row.negotiator?.name || '-',
  },
  {
    headerName: 'Added/Changed',
    field: 'updated_at',
    minWidth: 120,
    renderCell: ({ row }) => getFormattedDate(row.updated_at),
  },
  {
    headerName: 'Applied',
    field: 'applied_at',
    minWidth: 120,
    renderCell: ({ row }) => getFormattedDate(row.applied_at),
  },
  {
    headerName: 'Calculation status',
    field: 'calculation_status',
    minWidth: 120,
    renderCell: ({ row }) => (
      <CalculationStatus calculationStatus={row.calculation_status} />
    ),
  },
];

export const agreementsTableConfig = [...baseAgreementColumns];

export const agreementsTableCompareConfig = [
  ...baseAgreementColumns,
  ...compareColumns,
];

export const budgetCompareTableConfig = [
  {
    headerName: 'ID',
    field: agreementFields.id,
    minWidth: 60,
    renderCell: ({ row }) => row.id,
  },
  {
    headerName: 'Budget',
    field: 'budget',
    minWidth: 150,
    renderCell: ({ row }) => row.budget || '',
  },
  ...baseAgreementColumns.slice(1),
  ...compareColumns,
];
