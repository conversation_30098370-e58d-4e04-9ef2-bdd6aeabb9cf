import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { agreementsFiltersFields, defaultAgreementsFilters } from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableFilters/constants';
import { getFiltersDatesAdjustedBudgetDates } from 'features/BudgetItems/shared/utilities';

const useAgreementsForActionsFilters = (status, calculationStatus) => {
  const agreementsFiltersData = useSelector((state) => state.agreementsForActionsFilters.data);

  const budgetParametersData = useSelector((state) => state.budgetParameters.data);

  const [
    initialAgreementsFilters, setInitialAgreementsFilters,
  ] = useState(defaultAgreementsFilters);

  const [agreementsFilters, setAgreementsFilters] = useState(initialAgreementsFilters);

  useEffect(() => {
    const periodFilters = getFiltersDatesAdjustedBudgetDates(
      agreementsFiltersData, budgetParametersData,
    );

    const newInitialAgreementsFilters = {
      ...defaultAgreementsFilters,
      [agreementsFiltersFields.startDateMin]:
      periodFilters[agreementsFiltersFields.startDateMin],
      [agreementsFiltersFields.startDateMax]:
      periodFilters[agreementsFiltersFields.startDateMax],
      [agreementsFiltersFields.endDateMin]:
      periodFilters[agreementsFiltersFields.endDateMin],
      [agreementsFiltersFields.endDateMax]:
      periodFilters[agreementsFiltersFields.endDateMax],
      [agreementsFiltersFields.statuses]: status || [],
      [agreementsFiltersFields.calculationStatuses]: calculationStatus || [],
    };

    setInitialAgreementsFilters(newInitialAgreementsFilters);
    setAgreementsFilters(newInitialAgreementsFilters);
  }, [agreementsFiltersData, budgetParametersData]);

  return {
    initialAgreementsFilters, agreementsFilters, setAgreementsFilters,
  };
};

export default useAgreementsForActionsFilters;
