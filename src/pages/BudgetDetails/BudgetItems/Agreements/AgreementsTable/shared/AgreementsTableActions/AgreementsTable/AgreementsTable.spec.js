import React from 'react';
import {
  act, render, screen,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '@mui/material/styles';
import MockAdapter from 'axios-mock-adapter';
import {
  MuiTableConstants,
  MuiTableContext,
} from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import {
  defaultPageSize,
} from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/constants';

import { AppContextProvider } from 'AppContextProvider';
import mockAgreementsData from 'features/GetAgreements/mockAgreementsData';
import { ngaAxios } from 'core/services/HTTPService';
import getIds from 'core/utilities/getIds';
import getAgreementsUrl from 'features/GetAgreements/apiUrls';
import mockBudgetParametersData
  from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/mockBudgetParametersData';

import mockAgreementsFiltersData
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableFilters/GetAgreementsFilters/mockAgreementsFiltersData';
import AgreementsTable from './AgreementsTable';
import AgreementsTableProvider from './AgreementsTableProvider';

const { MuiTableProvider } = MuiTableContext;
const { firstPage } = MuiTableConstants;

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

jest.mock('pages/BudgetDetails/BudgetDetailsContextProvider', () => ({
  useBudgetDetailsContext: () => ({
    budgetId: 1,
    globalBudgetFilters: {},
  }),
}));

describe('BudgetDetails: BudgetItems: Agreements: AgreementsTable: AgreementTableActions: ModalToCopyAgreementFromAnotherBudget: AgreementTable', () => {
  const mockBudgetId = '1';
  const mockNgaAxios = new MockAdapter(ngaAxios);

  const testReducer = (additionalReducer) => combineReducers({
    agreementsForModalAction: () => ({
      data: mockAgreementsData,
      isLoading: true,
    }),
    budgetParameters: () => ({
      data: mockBudgetParametersData,
      isLoading: false,
    }),
    ...additionalReducer,
  });

  const store = (additionalReducer) => configureStore({
    reducer: testReducer(additionalReducer),
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }),
  });

  const agreementsTable = (additionalReducer) => (
    <Provider store={store(additionalReducer)}>
      <AppContextProvider>
        <ThemeProvider theme={theme(currentTheme)}>
          <AgreementsTableProvider
            budgetValue={mockBudgetId}
            agreementsFilters={mockAgreementsFiltersData}
            defaultPagination={{ page: firstPage, pageSize: defaultPageSize }}
          >
            <MuiTableProvider>
              <AgreementsTable />
            </MuiTableProvider>
          </AgreementsTableProvider>
        </ThemeProvider>
      </AppContextProvider>
    </Provider>
  );

  const renderTable = async (additionalReducer) => {
    await act(() => render(agreementsTable(additionalReducer)));
  };

  beforeEach(() => {
    mockNgaAxios.onGet(getAgreementsUrl(mockBudgetId)).reply(200, mockAgreementsData);
  });

  afterEach(() => {
    mockNgaAxios.reset();
  });

  test('should be mock agreements in the DOM', async () => {
    await renderTable();

    const firstItem = mockAgreementsData.results[0];
    const agreementName = screen.getByText(firstItem.name);

    expect(agreementName).toBeInTheDocument();
  });

  test('should be status label "Draft" if status value is "DRAFT"', async () => {
    await renderTable();

    const status = screen.getAllByText('Draft')[0];

    expect(status).toBeInTheDocument();
  });

  test('should be checkboxes for selection in the DOM', async () => {
    await renderTable();

    const checkbox = screen.getAllByRole('checkbox')[0];

    expect(checkbox).toBeInTheDocument();
  });

  test('should be call getAgreements action', async () => {
    await renderTable();

    expect(mockNgaAxios.history.get.length).toBe(1);
  });

  test('should be call getAgreements action with initial request params', async () => {
    await renderTable();

    expect(mockNgaAxios.history.get[0].params).toEqual({
      page: firstPage,
      page_size: defaultPageSize,
      search: '',
      budget_id: mockBudgetId,
      home_operators: getIds(mockBudgetParametersData.home_operators),
      ...mockAgreementsFiltersData,
    });
  });
});
