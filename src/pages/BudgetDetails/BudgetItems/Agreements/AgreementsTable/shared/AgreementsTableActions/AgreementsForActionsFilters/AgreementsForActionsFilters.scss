.agreements-for-actions-filters {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 30px;
  justify-content: space-between;

  &__title {
    padding: 5px 15px 24px 30px;
  }

  .light-button {
    width: 127px !important;
  }

  .calendar {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .scrollbar-container {
    height: calc(100% - 40px) !important;
    padding-right: 15px !important;
    margin-right: 10px !important;

    @media (max-width: $small-mobile-width) {
      max-height: calc(100% - 40px) !important;
    }
  }

  .agreements-table-filters-modal-content__actions {
    margin-top: 0 !important;
    flex-direction: row !important;
    gap: 20px !important;
  }

  .list-preloader__i {
    height: 40px !important;
    margin: 0 30px 30px !important;
    width: 281px !important;
  }
}
