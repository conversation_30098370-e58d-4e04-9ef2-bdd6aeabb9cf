import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { HTTPService } from 'core/services';
import getIds from 'core/utilities/getIds';
import getAgreementsForActionsFiltersAction from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/GetAgreementsToCopyFilters/actions';

let agreementsForActionsFiltersController = HTTPService.getController();
const useGetAgreementsForActionsFilters = (budgetId, isCurrentBudget = true) => {
  const dispatch = useDispatch();

  const budgetParametersData = useSelector((state) => state.budgetParameters.data);

  const params = {
    budget_id: budgetId,
    ...(isCurrentBudget && {
      home_operators: getIds(budgetParametersData.home_operators),
      start_date: budgetParametersData.start_date,
      end_date: budgetParametersData.end_date,
    }),
  };

  const cancelGetAgreementsForActionsFiltersRequest = () => {
    HTTPService.cancelRequest(agreementsForActionsFiltersController);
  };

  const getAgreementsForActionsFilters = async () => {
    cancelGetAgreementsForActionsFiltersRequest();

    agreementsForActionsFiltersController = HTTPService.getController();

    await dispatch(getAgreementsForActionsFiltersAction(
      agreementsForActionsFiltersController,
      params,
    ));
  };

  useEffect(() => {
    if (budgetId) {
      getAgreementsForActionsFilters();
    }

    return cancelGetAgreementsForActionsFiltersRequest;
  }, [budgetId]);
};

export default useGetAgreementsForActionsFilters;
