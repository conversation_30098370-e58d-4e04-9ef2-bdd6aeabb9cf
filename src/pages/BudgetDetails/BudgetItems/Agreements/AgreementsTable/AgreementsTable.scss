@import "src/pages/BudgetDetails/BudgetItems/shared/BudgetItemsTable";

$above-small-mobile-width: 660px;

.agreements-table {
  &__link {
    font-size: 12px;
    font-weight: bold !important;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;

    &:not(:hover) {
      color: $dark-color-500 !important;
    }
  }

  .budget-items-table__cell {
    .MuiInputBase-input {
      width: 100%;
    }

    .negotiator-autocomplete {
      width: 250px;
    }
  }

  .icon-card {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    transition: 0.2s;
    border-radius: 4px;

    & svg, & svg path {
      color: $dark-color-300 !important;
      stroke: $dark-color-300 !important;
    }
  }

  .interactions {
    @media (max-width: $tablet-width) {
      flex-wrap: wrap;
      gap: 15px;
    }

    &__actions {
      @media (max-width: $tablet-width) {
        flex: auto !important;
      }
    }
  }

  &__actions {
    display: flex !important;
    align-items: center;
    gap: 20px;

    .table-action-btn {
      position: static !important;
    }

    @media (max-width: $above-small-mobile-width) {
      position: absolute;
      top: 65px;
      right: 15px;
    }
  }

  .MuiDataGrid-root {
    @media (max-width: $above-small-mobile-width) {
      margin-top: 50px;
    }
  }
}
