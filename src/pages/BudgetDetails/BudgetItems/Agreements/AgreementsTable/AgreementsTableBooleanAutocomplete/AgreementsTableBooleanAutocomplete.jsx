import React from 'react';
import PropTypes from 'prop-types';
import Autocomplete from 'shared/Autocomplete';

const AgreementsTableBooleanAutocomplete = ({
  value,
  onChange,
  autocompleteOptions,
  className,
  ...props
}) => {
  const selectedOption = autocompleteOptions.find((option) => option.value === value) || null;

  const handleAutocompleteChange = (event, newValue) => {
    onChange(newValue ? newValue.value : null);
  };

  const getLabel = (option) => option?.label || '';

  const filterOptions = (options) => options;

  return (
    <Autocomplete
      className={className}
      data-testid={className}
      options={autocompleteOptions}
      value={selectedOption}
      onChange={handleAutocompleteChange}
      getLabel={getLabel}
      optionKey="value"
      filterOptions={filterOptions}
      clearOnEscape
      disableClearable
      {...props}
    />
  );
};

AgreementsTableBooleanAutocomplete.propTypes = {
  value: PropTypes.oneOf([true, false]),
  onChange: PropTypes.func,
  autocompleteOptions: PropTypes.oneOfType([
    PropTypes.shape({
      value: PropTypes.bool,
      label: PropTypes.string,
    }),
    PropTypes.instanceOf(Array),
  ]).isRequired,
  className: PropTypes.string,
};

AgreementsTableBooleanAutocomplete.defaultProps = {
  value: null,
  onChange: () => {},
  className: '',
};

export default AgreementsTableBooleanAutocomplete;
