$above-small-mobile-width: 660px;

.budget-items {
  margin-top: 15px !important;
  padding: 15px !important;

  &__tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .MuiBox-root {
      border-bottom: none !important;
      overflow-x: hidden !important;
    }

    @media (max-width: $above-small-mobile-width) {
      padding: 0 15px !important;
    }
  }

  &__toggle-button {
    & polyline {
      stroke: $dark-color-300;
    }

    &:hover {
      & polyline {
        stroke: $brand-blue-color-500;
      }
    }

    &[aria-selected="true"] {
      & svg {
        transform: rotate(180deg) !important;
        transition: transform 2ms;
      }
    }
  }

  &-tab {
    &__data {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      background-color: $light-color-100;
      border-radius: 4px;
      font-family: $primary-font-family;
      color: $dark-color-500;
      font-weight: 700;
    }

    &__quantity-preloader {
      width: 28px;
      height: 28px !important;
      background-color: $light-color-100 !important;
      border-radius: 4px;
    }
  }

  .MuiTabs-scroller {
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }
  }
}
