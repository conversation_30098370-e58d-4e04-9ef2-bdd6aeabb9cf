.budget-filters {
  &-modal {
    &__content {
      width: 450px;
      margin-top: 32px;

      @media (max-width: $small-mobile-width) {
        width: auto;
      }

      .autocomplete {
        width: 100% !important;
        margin-bottom: 32px;

        @media (max-width: $small-mobile-width) {
          margin-bottom: 30px;
        }
      }

      .countries-autocomplete {
        margin-bottom: 24px !important;
      }
    }

    &__divider {
      border-bottom: 2px $light-color-300;
      border-style: dotted;
      margin-bottom: 32px;

      @media (max-width: $small-mobile-width) {
        margin-bottom: 25px;
      }
    }

    &__period {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-bottom: 32px;
    }

    & .custom-modal__btn {
      width: 120px !important;
    }
  }

  &__open-btn {
    min-width: 40px !important;
  }

  &__counter {
    color: $white-color;
    height: 16px;
    width: 16px;
    padding: 10px;
    display: flex;
    margin-left: 5px;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
  }
}
