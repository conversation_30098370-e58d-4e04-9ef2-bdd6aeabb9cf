import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { AiOutlineClear } from 'react-icons/ai';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';

import LightButton from 'shared/LightButton';
import OperatorsAutocomplete from 'features/OperatorsAutocomplete';
import CountriesAutocomplete from 'features/CountriesAutocomplete';
import HomeOperatorsAutocomplete
  from 'pages/BudgetDetails/HomeOperatorsAutocomplete';
import { useBudgetDetailsContext } from 'pages/BudgetDetails/BudgetDetailsContextProvider';
import RangePicker from 'shared/RangePicker';
import getDisabledDate from 'core/utilities/getDisabledDate';
import FiltersButton from 'shared/FiltersButton';
import { isDateModified } from 'core/utilities/isDateModified';

import OperatorsAutocompleteByCountries from './OperatorsAutocompleteByCountries';
import {
  budgetFiltersFields,
  budgetFiltersModalTitle,
  budgetFiltersTitles,
} from './constants';
import BudgetFiltersSaving from './BudgetFiltersSaving';
import BudgetFiltersInfo from './BudgetFiltersInfo';
import PredefinedFilters from './PredefinedFilters';
import BudgetFiltersTitle from './shared/BudgetFiltersTitle';

import './BudgetFilters.scss';

const BudgetFilters = () => {
  const {
    fullGlobalBudgetFilters,
    resetFullGlobalBudgetFilters,
    updateFullGlobalBudgetFilters,
    isGlobalDateFilterModified,
    setIsGlobalDateFilterModified,
    setIsGlobalFiltersReset,
    countryView,
  } = useBudgetDetailsContext();

  const {
    start_date: initialStartDate,
    end_date: initialEndDate,
  } = useSelector((state) => state.budgetParameters.data);

  const [isOpenFiltersModal, setIsOpenFiltersModal] = useState(false);
  const [predefinedFilter, setPredefinedFilter] = useState(null);
  const [operators, setOperators] = useState([]);
  const [countries, setCountries] = useState([]);
  const [homeOperators, setHomeOperators] = useState([]);
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [isDisabledConfirmButton, setIsDisabledConfirmButton] = useState(true);
  const [isDateFilterChanged, setIsDateFilterChanged] = useState(false);
  const [globalFiltersCounter, setGlobalFiltersCounter] = useState(null);

  const limitTags = 3;

  const isCountryViewSelect = !!countryView?.id;

  const closeModal = () => {
    setIsOpenFiltersModal(false);
    setIsDateFilterChanged(false);
  };

  const defineAndSetGlobalBudgetFilters = () => {
    const newFullGlobalBudgetFilters = {
      [budgetFiltersFields.predefinedFilters]: predefinedFilter,
      [budgetFiltersFields.homeOperators]: homeOperators,
      [budgetFiltersFields.partnerOperators]: operators,
      [budgetFiltersFields.partnerCountries]: countries,
      ...(isGlobalDateFilterModified && {
        [budgetFiltersFields.startDate]: startDate,
        [budgetFiltersFields.endDate]: endDate,
      }),
    };

    updateFullGlobalBudgetFilters(newFullGlobalBudgetFilters);
  };

  const countFilters = () => {
    let fieldNumber = 0;

    if (fullGlobalBudgetFilters[budgetFiltersFields.homeOperators]?.length) {
      fieldNumber += 1;
    }

    if (!isCountryViewSelect) {
      if (fullGlobalBudgetFilters[budgetFiltersFields.partnerCountries]?.length) {
        fieldNumber += 1;
      }

      if (fullGlobalBudgetFilters[budgetFiltersFields.partnerOperators]?.length) {
        fieldNumber += 1;
      }
    }

    if (isDateFilterChanged || fullGlobalBudgetFilters[budgetFiltersFields.startDate]) {
      fieldNumber += 1;
    }

    setGlobalFiltersCounter(fieldNumber || null);
  };

  const confirmFilters = () => {
    defineAndSetGlobalBudgetFilters();

    closeModal();
  };

  const openFiltersModal = () => {
    setIsOpenFiltersModal(true);
    setIsGlobalFiltersReset(false);

    setPredefinedFilter(fullGlobalBudgetFilters[budgetFiltersFields.predefinedFilters]);
    setOperators(fullGlobalBudgetFilters[budgetFiltersFields.partnerOperators]);
    setCountries(fullGlobalBudgetFilters[budgetFiltersFields.partnerCountries]);
    setHomeOperators(fullGlobalBudgetFilters[budgetFiltersFields.homeOperators]);
    setStartDate(fullGlobalBudgetFilters[budgetFiltersFields.startDate] || initialStartDate);
    setEndDate(fullGlobalBudgetFilters[budgetFiltersFields.endDate] || initialEndDate);
  };

  const resetFiltration = () => {
    resetFullGlobalBudgetFilters();
    setIsGlobalFiltersReset(true);

    closeModal();
  };

  const checkIsConfirmButtonDisabled = () => {
    const isConfirmButtonEnabled = !!(
      countries.length || operators.length || homeOperators.length || isGlobalDateFilterModified
    );

    setIsDisabledConfirmButton(!isConfirmButtonEnabled);
  };

  const onCalendarChange = (calendarStartDate, calendarEndDate) => {
    setStartDate(calendarStartDate);
    setEndDate(calendarEndDate);

    setIsDateFilterChanged(true);
  };

  const disabledDate = getDisabledDate(initialStartDate, initialEndDate);

  useEffect(() => {
    checkIsConfirmButtonDisabled();
  }, [countries, operators, homeOperators, isGlobalDateFilterModified]);

  useEffect(() => {
    countFilters();
  }, [fullGlobalBudgetFilters, isCountryViewSelect]);

  useEffect(() => {
    const isModified = isDateModified({
      startDate,
      endDate,
      initialEndDate,
      initialStartDate,
    });

    setIsGlobalDateFilterModified(isModified);
  }, [startDate, endDate, initialEndDate, initialStartDate]);

  return (
    <div className="budget-filters">
      <FiltersButton
        globalFilters={fullGlobalBudgetFilters}
        filtersTitle={budgetFiltersModalTitle}
        openFiltersModal={openFiltersModal}
        tooltipText={budgetFiltersModalTitle}
        filtersCounter={globalFiltersCounter}
        isCountryViewSelect={isCountryViewSelect}
      />
      <CustomModal
        isOpen={isOpenFiltersModal}
        buttonDisable={isDisabledConfirmButton}
        onClickConfirm={confirmFilters}
        onClickCancel={closeModal}
        title={budgetFiltersModalTitle}
        handleOpen={setIsOpenFiltersModal}
        modalClass="budget-filters-modal"
      >
        <div className="budget-filters-modal__content">
          <BudgetFiltersTitle title={budgetFiltersTitles.predefinedFilter} />
          <PredefinedFilters
            predefinedFilter={predefinedFilter}
            setPredefinedFilter={setPredefinedFilter}
            setCountries={setCountries}
            setOperators={setOperators}
            setHomeOperators={setHomeOperators}
            setStartDate={setStartDate}
            setEndDate={setEndDate}
            initialStartDate={initialStartDate}
            initialEndDate={initialEndDate}
            setIsDateFilterChanged={setIsDateFilterChanged}
            countries={countries}
            operators={operators}
            homeOperators={homeOperators}
            startDate={startDate}
            endDate={endDate}
          />
          <div className="budget-filters-modal__divider" />
          <BudgetFiltersTitle title={budgetFiltersTitles.homeOperators} />
          <HomeOperatorsAutocomplete
            homeOperatorsValue={homeOperators}
            onHomeOperatorsChange={setHomeOperators}
            label="Home Operators"
            limitTags={limitTags}
          />
          <BudgetFiltersTitle title={budgetFiltersTitles.partner} />
          {isCountryViewSelect && (
          <BudgetFiltersInfo />
          )}
          <CountriesAutocomplete
            country={countries}
            onCountryChange={(e, value) => setCountries(value)}
            label="Partner Country"
            limitTags={limitTags}
            disabled={isCountryViewSelect}
          />
          {countries.length
            ? (
              <OperatorsAutocompleteByCountries
                selectedCountries={countries}
                selectedOperators={operators}
                setSelectedOperators={setOperators}
                disabled={isCountryViewSelect}
              />
            )
            : (
              <OperatorsAutocomplete
                operators={operators}
                onChangeOperators={(e, value) => setOperators(value)}
                label=" Partner Operator"
                limitTags={limitTags}
                disabled={isCountryViewSelect}
              />
            )}
        </div>
        <BudgetFiltersTitle title={budgetFiltersTitles.period} />
        <div className="budget-filters-modal__period">
          <RangePicker
            startDate={startDate}
            endDate={endDate}
            onCalendarChange={onCalendarChange}
            disabledDate={disabledDate}
          />
        </div>
        <LightButton
          action={resetFiltration}
          text="Reset filtration"
          startIcon={<AiOutlineClear />}
        />
        <BudgetFiltersSaving />
      </CustomModal>
    </div>
  );
};

export default BudgetFilters;
