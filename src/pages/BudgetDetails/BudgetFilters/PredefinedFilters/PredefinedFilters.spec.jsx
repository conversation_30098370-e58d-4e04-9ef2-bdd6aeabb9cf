import React from 'react';
import { render } from '@testing-library/react';
import { useSelector } from 'react-redux';

import {
  budgetFiltersFields,
  customPredefinedFilterOption,
} from 'pages/BudgetDetails/BudgetFilters/constants';

import PredefinedFilters from './PredefinedFilters';
import PredefinedFiltersAutocomplete from './PredefinedFiltersAutocomplete';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('./PredefinedFiltersAutocomplete', () => jest.fn(() => null));

describe('BudgetDetails: BudgetFilters: PredefinedFilters', () => {
  const mockFilters = [
    {
      id: '-1',
      name: 'Custom',
      [budgetFiltersFields.startDate]: '2024-01',
      [budgetFiltersFields.endDate]: '2024-01',
      [budgetFiltersFields.partnerCountries]: [{ id: 'c1' }],
      [budgetFiltersFields.partnerOperators]: [{ id: 'o1' }],
      [budgetFiltersFields.homeOperators]: [{ id: 'h1' }],
    },
  ];

  const props = {
    predefinedFilter: mockFilters[0],
    setPredefinedFilter: jest.fn(),
    setCountries: jest.fn(),
    setOperators: jest.fn(),
    setHomeOperators: jest.fn(),
    setStartDate: jest.fn(),
    setEndDate: jest.fn(),
    initialStartDate: '2024-02',
    initialEndDate: '2024-02',
    countries: [{ id: 'c1' }],
    operators: [{ id: 'o1' }],
    homeOperators: [{ id: 'h1' }],
    startDate: '2024-01',
    endDate: '2024-01',
  };

  beforeEach(() => {
    useSelector.mockReturnValue(mockFilters);
    PredefinedFiltersAutocomplete.mockClear();
  });

  test('should render and pass predefined filters to PredefinedFiltersAutocomplete', () => {
    render(<PredefinedFilters {...props} />);

    expect(PredefinedFiltersAutocomplete).toHaveBeenCalledWith(
      expect.objectContaining({
        predefinedFilter: props.predefinedFilter,
        setPredefinedFilter: expect.any(Function),
        predefinedFilters: expect.any(Array),
      }),
      {},
    );
  });

  test('should call all setters with selected filter values when a new filter is selected', () => {
    render(<PredefinedFilters {...props} />);

    const autocompleteProps = PredefinedFiltersAutocomplete.mock.calls[0][0];

    autocompleteProps.setPredefinedFilter(mockFilters[0]);

    expect(props.setPredefinedFilter).toHaveBeenCalledWith(mockFilters[0]);
    expect(props.setStartDate).toHaveBeenCalledWith('2024-01');
    expect(props.setEndDate).toHaveBeenCalledWith('2024-01');
    expect(props.setCountries).toHaveBeenCalledWith([{ id: 'c1' }]);
    expect(props.setOperators).toHaveBeenCalledWith([{ id: 'o1' }]);
    expect(props.setHomeOperators).toHaveBeenCalledWith([{ id: 'h1' }]);
  });

  test('should include customPredefinedFilterOption in predefinedFilters list', () => {
    render(<PredefinedFilters {...props} />);

    const passedFilters = PredefinedFiltersAutocomplete.mock.calls[0][0].predefinedFilters;

    const hasCustom = passedFilters.some((f) => f.label === customPredefinedFilterOption.label,
    );

    expect(hasCustom).toBe(true);
  });

  test('should set filter to customPredefinedFilterOption if values no longer match predefined', () => {
    const modifiedProps = {
      ...props,
      countries: [{ id: 'DIFFERENT' }],
      operators: [],
      homeOperators: [],
      startDate: '2028-01',
      endDate: '2028-01',
    };

    render(<PredefinedFilters {...modifiedProps} />);

    expect(props.setPredefinedFilter)
      .toHaveBeenCalledWith(expect.objectContaining(customPredefinedFilterOption));
  });
});
