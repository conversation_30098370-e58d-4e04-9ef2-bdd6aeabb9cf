import React from 'react';
import PropTypes from 'prop-types';

import { TextField } from '@mui/material';

import Autocomplete from 'shared/Autocomplete';
import {
  customPredefinedFilterOption,
} from 'pages/BudgetDetails/BudgetFilters/constants';

import {
  predefinedFiltersAutocompleteModifier,
  predefinedFiltersAutocompleteWrapModifier,
  optionKey,
} from './constants';

const PredefinedFiltersAutocomplete = ({
  predefinedFilter,
  setPredefinedFilter,
  predefinedFilters,
}) => {
  const onChangePredefinedFilter = (e, nextValue) => {
    setPredefinedFilter(nextValue);
  };

  return (
    <div className={predefinedFiltersAutocompleteWrapModifier}>
      <Autocomplete
        className={predefinedFiltersAutocompleteModifier}
        data-testid={predefinedFiltersAutocompleteModifier}
        autoComplete={false}
        selectOnFocus={false}
        options={predefinedFilters}
        getOptionLabel={(option) => (option.name ? option.name : '')}
        isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
        value={predefinedFilter}
        onChange={onChangePredefinedFilter}
        renderInput={(params) => <TextField {...params} placeholder="Search" label="Filters" />}
        optionKey={optionKey}
        disableClearable
        getLabel={(option) => option.name}
      />
    </div>
  );
};

PredefinedFiltersAutocomplete.propTypes = {
  predefinedFilter: PropTypes.instanceOf(Object),
  setPredefinedFilter: PropTypes.func,
  predefinedFilters: PropTypes.instanceOf(Array),
};

PredefinedFiltersAutocomplete.defaultProps = {
  predefinedFilter: customPredefinedFilterOption,
  setPredefinedFilter: () => {},
  predefinedFilters: [],
};

export default PredefinedFiltersAutocomplete;
