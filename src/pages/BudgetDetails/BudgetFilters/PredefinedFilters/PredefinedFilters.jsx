import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import { isEqual } from 'lodash';

import {
  budgetFiltersFields,
  customPredefinedFilterOption,
} from 'pages/BudgetDetails/BudgetFilters/constants';
import {
  defaultGlobalBudgetFilters,
} from 'pages/BudgetDetails/BudgetFilters/hooks/useGlobalBudgetFilters';

import PredefinedFiltersAutocomplete from './PredefinedFiltersAutocomplete';

const PredefinedFilters = ({
  predefinedFilter,
  setPredefinedFilter,
  setCountries,
  setOperators,
  setHomeOperators,
  setStartDate,
  setEndDate,
  initialStartDate,
  initialEndDate,
  countries,
  operators,
  homeOperators,
  startDate,
  endDate,
}) => {
  const defaultPredefinedFilters = useSelector((state) => state.predefinedFilters.data) || [];
  const predefinedFilters = [
    ...defaultPredefinedFilters,
    {
      ...defaultGlobalBudgetFilters,
      ...customPredefinedFilterOption,
      [budgetFiltersFields.startDate]: initialStartDate,
      [budgetFiltersFields.endDate]: initialEndDate,
    },
  ];

  const getPredefinedFilterSettings = (
    currentFilter,
  ) => predefinedFilters.find(({ id }) => id === currentFilter.id);

  const setPredefinedFilterSettings = (newPredefinedFilter) => {
    const predefinedFilterSettings = getPredefinedFilterSettings(newPredefinedFilter);

    setStartDate(predefinedFilterSettings[budgetFiltersFields.startDate]);
    setEndDate(predefinedFilterSettings[budgetFiltersFields.endDate]);
    setCountries(predefinedFilterSettings[budgetFiltersFields.partnerCountries]);
    setOperators(predefinedFilterSettings[budgetFiltersFields.partnerOperators]);
    setHomeOperators(predefinedFilterSettings[budgetFiltersFields.homeOperators]);
  };

  const checkValuesAccordanceToPredefinedFilterSettings = () => {
    const predefinedFilterSettings = getPredefinedFilterSettings(predefinedFilter);

    const valuesAccordance = isEqual(
      predefinedFilterSettings[budgetFiltersFields.partnerCountries], countries)
    && isEqual(predefinedFilterSettings[budgetFiltersFields.partnerOperators], operators)
    && isEqual(predefinedFilterSettings[budgetFiltersFields.homeOperators], homeOperators)
    && isEqual(predefinedFilterSettings[budgetFiltersFields.startDate], startDate)
    && isEqual(predefinedFilterSettings[budgetFiltersFields.endDate], endDate);

    const isNotCustomPredefinedFilter = predefinedFilter.id !== customPredefinedFilterOption.id;

    if (!valuesAccordance && isNotCustomPredefinedFilter) {
      setPredefinedFilter(customPredefinedFilterOption);
    }
  };

  useEffect(() => {
    checkValuesAccordanceToPredefinedFilterSettings();
  }, [countries, operators, homeOperators, startDate, endDate]);

  return (
    <PredefinedFiltersAutocomplete
      predefinedFilter={predefinedFilter}
      setPredefinedFilter={(value) => {
        setPredefinedFilterSettings(value);
        setPredefinedFilter(value);
      }}
      predefinedFilters={predefinedFilters}
    />
  );
};

PredefinedFilters.propTypes = {
  predefinedFilter: PropTypes.instanceOf(Object),
  setPredefinedFilter: PropTypes.func,
  setCountries: PropTypes.func,
  setOperators: PropTypes.func,
  setHomeOperators: PropTypes.func,
  setStartDate: PropTypes.func,
  setEndDate: PropTypes.func,
  initialStartDate: PropTypes.string,
  initialEndDate: PropTypes.string,
  countries: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
  operators: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
  homeOperators: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
  startDate: PropTypes.string,
  endDate: PropTypes.string,
};

PredefinedFilters.defaultProps = {
  predefinedFilter: customPredefinedFilterOption,
  setPredefinedFilter: () => {},
  setCountries: () => {},
  setOperators: () => {},
  setHomeOperators: () => {},
  setStartDate: () => {},
  setEndDate: () => {},
  initialStartDate: '',
  initialEndDate: '',
  countries: [],
  operators: [],
  homeOperators: [],
  startDate: '',
  endDate: '',
};

export default PredefinedFilters;
