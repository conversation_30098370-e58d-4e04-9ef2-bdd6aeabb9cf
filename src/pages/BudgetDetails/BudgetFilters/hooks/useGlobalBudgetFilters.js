import { useEffect, useState } from 'react';

import LocalStorageService from 'core/services/LocalStorageService';
import getIds from 'core/utilities/getIds';
import {
  budgetFiltersFields,
  customPredefinedFilterOption,
} from 'pages/BudgetDetails/BudgetFilters/constants';
import { getBudgetFiltersLocalStorageKey } from 'pages/BudgetDetails/BudgetFilters/utilities';

export const defaultGlobalBudgetFilters = {
  [budgetFiltersFields.predefinedFilters]: customPredefinedFilterOption,
  [budgetFiltersFields.homeOperators]: [],
  [budgetFiltersFields.partnerOperators]: [],
  [budgetFiltersFields.partnerCountries]: [],
  [budgetFiltersFields.startDate]: '',
  [budgetFiltersFields.endDate]: '',
};

const useGlobalBudgetFilters = ({
  budgetId,
}) => {
  const localStorageKeyName = getBudgetFiltersLocalStorageKey(budgetId);
  const initialLocalStorageValue = LocalStorageService.getItem(localStorageKeyName);

  const [
    isFullGlobalBudgetFiltersSaved,
    setIsFullGlobalBudgetFiltersSaved,
  ] = useState(!!initialLocalStorageValue);

  const initialFullGlobalBudgetFilters = isFullGlobalBudgetFiltersSaved
    ? {
      ...defaultGlobalBudgetFilters,
      ...initialLocalStorageValue,
    }
    : defaultGlobalBudgetFilters;

  const [
    fullGlobalBudgetFilters,
    setFullGlobalBudgetFilters,
  ] = useState(initialFullGlobalBudgetFilters);
  const [globalBudgetFilters, setGlobalBudgetFilters] = useState(null);

  const initGlobalBudgetFilters = () => {
    const startDate = fullGlobalBudgetFilters[budgetFiltersFields.startDate];
    const endDate = fullGlobalBudgetFilters[budgetFiltersFields.endDate];

    const newGlobalBudgetFilters = {
      [budgetFiltersFields.homeOperators]: getIds(
        fullGlobalBudgetFilters[budgetFiltersFields.homeOperators],
      ),
    };

    if (fullGlobalBudgetFilters[budgetFiltersFields.partnerOperators].length) {
      newGlobalBudgetFilters[budgetFiltersFields.partnerOperators] = getIds(fullGlobalBudgetFilters[
        budgetFiltersFields.partnerOperators
      ]);
    } else if (fullGlobalBudgetFilters[budgetFiltersFields.partnerCountries].length) {
      newGlobalBudgetFilters[budgetFiltersFields.partnerCountries] = getIds(
        fullGlobalBudgetFilters[budgetFiltersFields.partnerCountries],
      );
    }

    if (startDate) {
      newGlobalBudgetFilters[budgetFiltersFields.startDate] = startDate;
    }

    if (endDate) {
      newGlobalBudgetFilters[budgetFiltersFields.endDate] = endDate;
    }

    setGlobalBudgetFilters(newGlobalBudgetFilters);
  };

  const resetFullGlobalBudgetFilters = () => {
    setFullGlobalBudgetFilters(defaultGlobalBudgetFilters);
    LocalStorageService.removeItem(localStorageKeyName);
  };

  const updateFullGlobalBudgetFilters = (value) => {
    setFullGlobalBudgetFilters({
      ...defaultGlobalBudgetFilters,
      ...value,
    });

    if (isFullGlobalBudgetFiltersSaved) {
      LocalStorageService.setItem(localStorageKeyName, value);
    } else {
      LocalStorageService.removeItem(localStorageKeyName);
    }
  };

  useEffect(() => {
    initGlobalBudgetFilters();
  }, [fullGlobalBudgetFilters]);

  return {
    globalBudgetFilters,
    isFullGlobalBudgetFiltersSaved,
    setIsFullGlobalBudgetFiltersSaved,
    resetFullGlobalBudgetFilters,
    fullGlobalBudgetFilters,
    updateFullGlobalBudgetFilters,
  };
};

export default useGlobalBudgetFilters;
