import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

import { useDispatch, useSelector } from 'react-redux';
import { intersectionWith, isEqual, isNull } from 'lodash';
import { TextField } from '@mui/material';

import { HTTPService } from 'core/services';
import Autocomplete from 'shared/Autocomplete';
import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsAction from 'features/OperatorsAutocomplete/GetOperators/actions';
import getArrayByKey from 'core/utilities/getArrayByKey';

import {
  operatorsAutocompleteModifier,
  limitTags,
  optionKey,
  operatorsAutocompleteWrapModifier,
} from './constants';

let listOperatorsController = new AbortController();

const OperatorsAutocompleteByCountries = ({
  selectedCountries,
  selectedOperators,
  setSelectedOperators,
  readonly,
  disabled,
}) => {
  const dispatch = useDispatch();
  const listOfOperators = useSelector((state) => state.operators.data) || [];
  const isListOfOperatorsLoading = useSelector((state) => state.operators.isLoading);

  const getListOfOperatorsByCountry = () => {
    const params = {
      country_codes: getArrayByKey(selectedCountries, 'code'),
    };

    HTTPService.cancelRequest(listOperatorsController);

    listOperatorsController = HTTPService.getController();
    dispatch(getOperatorsAction(listOperatorsController, params));
  };

  const onChangeOperators = (e, nextValue) => {
    setSelectedOperators(nextValue);
  };

  const initSelectedOperators = () => {
    if (isListOfOperatorsLoading || isNull(listOfOperators)) {
      return;
    }

    const newSelectedOperators = intersectionWith(listOfOperators, selectedOperators, isEqual);

    setSelectedOperators(newSelectedOperators);
  };

  useEffect(() => {
    getListOfOperatorsByCountry();
  }, [selectedCountries]);

  useEffect(() => {
    initSelectedOperators();
  }, [listOfOperators]);

  return (
    <div className={operatorsAutocompleteWrapModifier}>
      <Autocomplete
        className={operatorsAutocompleteModifier}
        data-testid={operatorsAutocompleteModifier}
        multiple
        limitTags={limitTags}
        autoComplete={false}
        selectOnFocus={false}
        options={listOfOperators || []}
        getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
        isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
        value={selectedOperators}
        onChange={onChangeOperators}
        renderInput={(params) => <TextField {...params} placeholder="Search" label="Select operators" />}
        optionKey={optionKey}
        disableClearable
        readOnly={readonly}
        getLabel={(option) => <OperatorsLabel operator={option} />}
        disabled={disabled}
      />
    </div>
  );
};

OperatorsAutocompleteByCountries.propTypes = {
  selectedCountries: PropTypes.instanceOf(Array),
  selectedOperators: PropTypes.instanceOf(Array),
  setSelectedOperators: PropTypes.func,
  readonly: PropTypes.bool,
  disabled: PropTypes.bool,
};

OperatorsAutocompleteByCountries.defaultProps = {
  selectedCountries: [],
  selectedOperators: [],
  setSelectedOperators: () => {},
  readonly: false,
  disabled: false,
};

export default OperatorsAutocompleteByCountries;
