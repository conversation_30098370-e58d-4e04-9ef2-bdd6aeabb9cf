import React from 'react';
import { AppContextProvider } from 'AppContextProvider';
import {
  fireEvent, render, screen, waitFor, within,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { combineReducers, configureStore } from '@reduxjs/toolkit';
import MockAdapter from 'axios-mock-adapter';
import mockBudgetParametersData from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/mockBudgetParametersData';
import { ngaAxios } from 'core/services/HTTPService';
import getBudgetParametersUrl from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/apiUrls';
import * as BudgetDetailsContext from 'pages/BudgetDetails/BudgetDetailsContextProvider';
import mockCountriesData
  from 'features/CountriesAutocomplete/GetCountries/mockCountriesData';
import mockOperatorsList
  from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/mockOperatorsList';
import { dateFormat, rangePickerModifier } from 'shared/RangePicker/constants';
import dayjs from 'dayjs';
import { act } from 'react-dom/test-utils';
import BudgetFilters from './BudgetFilters';
import {
  defaultGlobalBudgetFilters,
} from './hooks/useGlobalBudgetFilters';

describe('BudgetDetails: BudgetFilters', () => {
  const updateFullGlobalBudgetFiltersMock = jest.fn();
  const resetFullGlobalBudgetFiltersMock = jest.fn();

  const selectedFilterPeriod = {
    startDate: '2024-05',
    endDate: '2024-06',
  };

  const contextValues = {
    fullGlobalBudgetFilters: defaultGlobalBudgetFilters,
    resetFullGlobalBudgetFilters: resetFullGlobalBudgetFiltersMock,
    updateFullGlobalBudgetFilters: updateFullGlobalBudgetFiltersMock,
    isGlobalDateFilterModified: false,
    setIsGlobalDateFilterModified: jest.fn(),
    setIsGlobalFiltersReset: jest.fn(),
  };
  jest.spyOn(BudgetDetailsContext, 'useBudgetDetailsContext').mockImplementation(() => contextValues);

  const testReducer = combineReducers({
    budgetParameters: () => ({
      data: mockBudgetParametersData,
      isLoading: false,
    }),
    operators: () => ({
      data: mockOperatorsList,
      isLoading: false,
    }),
    countries: () => ({
      data: mockCountriesData,
      isLoading: false,
    }),
    predefinedFilters: () => ({
      data: [],
    }),
  });

  const store = configureStore({
    reducer: testReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }),
  });

  const geBudgetFilters = () => (
    <Provider store={store}>
      <AppContextProvider>
        <BudgetFilters />
      </AppContextProvider>
    </Provider>
  );

  const mockNgaAxios = new MockAdapter(ngaAxios);
  const mockRequestsForDefaultRender = () => {
    mockNgaAxios.onGet(getBudgetParametersUrl(1)).reply(200, mockBudgetParametersData);
  };

  const openFiltersModal = () => {
    const filtersOpenButton = screen.getByTestId('filters-btn');

    fireEvent.click(filtersOpenButton);
  };

  const confirm = () => {
    const confirmBtn = screen.getByText('Confirm');

    fireEvent.click(confirmBtn);
  };

  const selectAutocompleteValue = ({
    testId, mockFirstOptionVal,
  }) => {
    const autocompleteEl = screen.getByTestId(testId);
    const input = within(autocompleteEl).getByRole('combobox');
    const mockOptionVal = mockFirstOptionVal;

    fireEvent.change(input, { target: { value: mockOptionVal } });
    fireEvent.keyDown(autocompleteEl, { key: 'ArrowDown' });
    fireEvent.keyDown(autocompleteEl, { key: 'ArrowDown' });
    fireEvent.keyDown(autocompleteEl, { key: 'Enter' });
  };

  const selectHomeOperator = () => {
    selectAutocompleteValue({
      testId: 'home-operators-autocomplete',
      mockFirstOptionVal: mockBudgetParametersData.home_operators[0].pmn_code,
    });
  };

  const selectCountry = () => {
    selectAutocompleteValue({
      testId: 'countries-autocomplete',
      mockFirstOptionVal: mockCountriesData[0].code,
    });
  };

  const selectPartnerCountryOperator = () => {
    selectAutocompleteValue({
      testId: 'operators-autocomplete-by-countries',
      mockFirstOptionVal: mockOperatorsList[0].pmn_code,
    });
  };

  const selectPeriod = () => {
    const [startDateInput, endDateInput] = screen.getAllByTestId(rangePickerModifier);

    fireEvent.change(startDateInput, { target: { value: selectedFilterPeriod.startDate } });
    fireEvent.change(endDateInput, { target: { value: selectedFilterPeriod.endDate } });
  };

  beforeEach(() => {
    mockNgaAxios.reset();
    mockRequestsForDefaultRender();
  });

  test('should be Budget Filters Button in the DOM', () => {
    render(geBudgetFilters());

    expect(screen.getByTestId('filters-btn')).toBeInTheDocument();
  });

  test('should be Budget Filters Modal in the DOM', () => {
    render(geBudgetFilters());
    openFiltersModal();

    expect(screen.getByTestId('custom-modal')).toBeInTheDocument();
  });

  test('should be closed Budget  Filters Modal after click Cancel button', () => {
    render(geBudgetFilters());
    openFiltersModal();

    const modal = screen.getByTestId('custom-modal');

    const cancelButton = screen.getByText('Cancel');

    fireEvent.click(cancelButton);

    waitFor(() => expect(modal).not.toBeInTheDocument());
  });

  test('should call updateFullGlobalBudgetFiltersMock with initial filters when user click "Confirm" button', async () => {
    render(geBudgetFilters());
    openFiltersModal();
    selectHomeOperator();
    confirm();

    expect(updateFullGlobalBudgetFiltersMock).toBeCalledWith(
      {
        home_operators: [mockBudgetParametersData.home_operators[0]],
        partner_operators: [],
        partner_countries: [],
        predefined_filters: {
          id: '-1',
          name: 'Custom',
        },
      },
    );
  });

  test('should call updateFullGlobalBudgetFiltersMock with period filters when user click "Confirm" button', async () => {
    render(geBudgetFilters());
    openFiltersModal();
    selectPeriod();
    confirm();

    expect(contextValues.setIsGlobalDateFilterModified).toHaveBeenCalledWith(true);
  });

  test('should call updateFullGlobalBudgetFiltersMock with home operators and only country filters when user click "Confirm" button', async () => {
    render(geBudgetFilters());
    openFiltersModal();
    selectHomeOperator();
    await act(() => {
      selectCountry();
    });
    confirm();

    expect(updateFullGlobalBudgetFiltersMock).toBeCalledWith(
      {
        home_operators: [mockBudgetParametersData.home_operators[0]],
        partner_countries: [mockCountriesData[0]],
        partner_operators: [],
        predefined_filters: {
          id: '-1',
          name: 'Custom',
        },
      },
    );
  });

  test('should call updateFullGlobalBudgetFiltersMock with home operators, operators, countries, period filters when user click "Confirm" button', async () => {
    jest
      .spyOn(BudgetDetailsContext, 'useBudgetDetailsContext')
      .mockImplementation(() => ({
        ...contextValues,
        isGlobalDateFilterModified: true,
      }));

    render(geBudgetFilters());
    openFiltersModal();
    selectHomeOperator();
    selectPeriod();
    await act(() => {
      selectCountry();
    });
    selectPartnerCountryOperator();
    confirm();

    expect(updateFullGlobalBudgetFiltersMock).toBeCalledWith(
      {
        home_operators: [mockBudgetParametersData.home_operators[0]],
        partner_operators: [mockOperatorsList[0]],
        partner_countries: [mockCountriesData[0]],
        start_date: selectedFilterPeriod.startDate,
        end_date: selectedFilterPeriod.endDate,
        predefined_filters: {
          id: '-1',
          name: 'Custom',
        },
      },
    );
  });

  test('should call resetFullGlobalBudgetFiltersMock when user click "Reset" button', async () => {
    render(geBudgetFilters());
    openFiltersModal();
    selectHomeOperator();

    const resetFiltrationBtn = screen.getByText('Reset filtration');

    fireEvent.click(resetFiltrationBtn);

    expect(resetFullGlobalBudgetFiltersMock).toBeCalled();
  });

  test('should have initial RangePicker data from mockBudgetParametersData', () => {
    render(geBudgetFilters());
    openFiltersModal();

    const [startDateInput, endDateInput] = screen.getAllByTestId(rangePickerModifier);

    expect(startDateInput).toHaveValue(mockBudgetParametersData.start_date);
    expect(endDateInput).toHaveValue(mockBudgetParametersData.end_date);
  });

  test('should disable dates outside the mockBudgetParametersData period in RangePicker', async () => {
    render(geBudgetFilters());
    openFiltersModal();

    const [startDateInput] = screen.getAllByTestId(rangePickerModifier);

    fireEvent.click(startDateInput);

    const calendarCells = document.querySelectorAll('.ant-picker-cell');

    const startDate = dayjs(mockBudgetParametersData.start_date, dateFormat);
    const endDate = dayjs(mockBudgetParametersData.end_date, dateFormat);

    calendarCells.forEach((cell) => {
      const cellTitle = cell.getAttribute('title');
      const cellDate = dayjs(cellTitle, dateFormat);

      if (cellDate.isBefore(startDate, 'month') || cellDate.isAfter(endDate, 'month')) {
        expect(cell).toHaveClass('ant-picker-cell-disabled');
      } else {
        expect(cell).not.toHaveClass('ant-picker-cell-disabled');
      }
    });
  });

  test('should ignore partner_countries and partner_operators in filters counter when countryView.id is truthy', async () => {
    jest
      .spyOn(BudgetDetailsContext, 'useBudgetDetailsContext')
      .mockImplementation(() => ({
        ...contextValues,
        fullGlobalBudgetFilters: {
          ...defaultGlobalBudgetFilters,
          partner_countries: [mockCountriesData[0]],
          partner_operators: [mockOperatorsList[0]],
          home_operators: [mockBudgetParametersData.home_operators[0]],
        },
        countryView: { id: 123 },
      }));

    render(geBudgetFilters());

    const filtersButton = screen.getByTestId('filters-btn');

    await waitFor(() => {
      expect(filtersButton).toHaveTextContent('1');
    });
  });
});
