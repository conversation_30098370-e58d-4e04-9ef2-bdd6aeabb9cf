import React from 'react';
import agreementStatuses from 'shared/AgreementStatusLabel/constants';
import { useAgreementParametersActionsContext } from './AgreementParametersActionsProvider';
import { agreementsParametersActionsModifier } from './constants';
import AgreementParametersEditModal from './AgreementParametersEditModal';
import AgreementParametersEditOperation from './AgreementParametersEditOperation';
import AgreementParametersOperationsGroup from './AgreementParametersOperationsGroup';

const AgreementParametersActions = () => {
  const {
    agreementParameters, isMasterBudget,
  } = useAgreementParametersActionsContext();

  const { status } = agreementParameters;

  const isEditAvailableByStatus = status === agreementStatuses.life
  || status === agreementStatuses.closed
  || status === agreementStatuses.approved
  || status === agreementStatuses.submitted
  || status === agreementStatuses.autoRenewed
  || status === agreementStatuses.budgeting;

  const isEditAvailableInMaster = isMasterBudget && isEditAvailableByStatus;

  return (
    <div
      className={agreementsParametersActionsModifier}
      data-testid={agreementsParametersActionsModifier}
    >
      {isEditAvailableInMaster && <AgreementParametersEditOperation />}
      {!isMasterBudget && (
      <AgreementParametersOperationsGroup
        isEditDisabled={isEditAvailableByStatus}
      />
      )}
      <AgreementParametersEditModal />
    </div>
  );
};

export default AgreementParametersActions;
