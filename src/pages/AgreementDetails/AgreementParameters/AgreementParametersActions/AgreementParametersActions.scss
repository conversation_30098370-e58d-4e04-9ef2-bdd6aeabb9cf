.agreement-parameters-actions {
  &__modal {
    &-content {
      margin-top: 20px !important;
    }
  }

  &__operation {
    &-edit {
      min-width: 40px !important;
    }
  }

  &__edit-modal {
    padding: 0 !important;

    .custom-modal__title-wrap {
      padding: 32px 32px 0 !important;
      gap: 20px;
    }

    .custom-modal__title {
      white-space: nowrap !important;

      @media (max-width: $small-mobile-width) {
        font-size: 16px !important;
      }
    }

    &-content {
      margin-top: 32px;
      display: flex;
      flex-direction: column;
      gap: 32px;
      padding: 0 32px;

      .negotiator-autocomplete, .agreement-statuses-autocomplete {
        width: 350px;

        @media (max-width: $small-mobile-width) {
          width: 100%;
        }
      }

      .agreement-statuses-autocomplete {
        .MuiInputBase-root {
          padding-left: 8px !important;
        }

        .MuiInputBase-input {
          margin-left: 0 !important;
        }
      }

      .MuiFormLabel-root {
        font-size: 12px !important;
      }
    }

    &-btn {
      width: 106px;

      &-wrap {
        display: flex;
        gap: 20px;
        padding: 32px !important;
      }
    }

    &-rolling-label {
      text-transform: uppercase !important;
      font-weight: 700 !important;
      color: $dark-color-400 !important;
    }

    &-name {
      .MuiFormLabel-root {
        transform: translate(12px, 12px) scale(1) !important;
        background-color: $white-color !important;
        padding: 0 5px !important;
      }

      .MuiOutlinedInput-input {
        color: $dark-color-300 !important;
      }
    }
  }

  .actions__btn-open-popover {
    &:disabled {
      background-color: $light-color-100 !important;

      svg {
        fill: $dark-color-200 !important;
      }
    }
  }
}

.MuiButton-startIcon {
  .edit-icon path {
    fill: none !important;
  }
}
