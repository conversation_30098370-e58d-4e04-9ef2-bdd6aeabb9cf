import React from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';

import { budgetTypes } from 'core/contstants';
import ActionsButtonWithPopover from 'shared/ActionsButtonWithPopover';
import { useAppContext } from 'AppContextProvider';
import {
  actions,
  modifiers,
} from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/constants';
import { getActionsConfig } from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/utilites';
import AgreementParametersConfirmationModal from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/AgreementParametersConfirmationModal';
import { useAgreementParametersActionsContext } from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/AgreementParametersActionsProvider';
import isNonRemovableAgreement from 'core/utilities/isNonRemovableAgreement';
import useActiveBudgetBackgroundJob
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useActiveBudgetBackgroundJob';
import useBudgetCalculationProcess
  from 'features/BudgetCalculation/hooks/useBudgetCalculationProcess';

import TooltipTitle from './TooltipTitle';

const AgreementParametersOperationsGroup = ({
  isEditDisabled,
}) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const budgetType = useSelector((state) => state.budgetParameters.data?.type);

  const {
    openActionModal,
    setCurrentAction,
    setIsEditModalOpen,
    agreementParameters,
  } = useAgreementParametersActionsContext();

  const { isActiveBackgroundJob } = useActiveBudgetBackgroundJob();

  const { isBudgetCalculationInProcess } = useBudgetCalculationProcess();

  const isAvailableByBudgetType = budgetType === budgetTypes.frozen;

  const isDisabled = isAvailableByBudgetType
      || isActiveBackgroundJob
      || isBudgetCalculationInProcess;

  const { status } = agreementParameters;

  const isDeleteDisabled = isNonRemovableAgreement(status);

  const onClickActivateAgreement = () => {
    setCurrentAction(actions.activate);
    openActionModal();
  };

  const onClickDeactivateAgreement = () => {
    setCurrentAction(actions.deactivate);
    openActionModal();
  };

  const onClickRenewAgreement = () => {
    setCurrentAction(actions.renew);
    openActionModal();
  };

  const onClickDeleteAgreement = () => {
    setCurrentAction(actions.delete);
    openActionModal();
  };

  const onClickEditAgreement = () => {
    setIsEditModalOpen(true);
  };

  const onClickCloneAgreement = () => {
    setCurrentAction(actions.clone);
    openActionModal();
  };

  const actionsConfig = getActionsConfig(
    agreementParameters, onClickActivateAgreement,
    onClickDeactivateAgreement, onClickRenewAgreement,
    onClickDeleteAgreement, onClickEditAgreement,
    onClickCloneAgreement, isEditDisabled, isDeleteDisabled,
  );

  const additionalPopoverStyles = {
    backgroundColor: `${getBrandColors(primaryColor)[50]} !important`,
    '& svg': {
      fill: primaryColor,
    },
    '&:hover, &.active': {
      backgroundColor: `${getBrandColors(primaryColor)[100]} !important`,
    },
  };

  return (
    <div
      className={modifiers.operationsGroup}
      data-testid={modifiers.operationsGroup}
    >
      <ActionsButtonWithPopover
        actionsConfig={actionsConfig}
        additionalStyles={additionalPopoverStyles}
        disabled={isDisabled}
        tooltipPlacement="left"
        btnOpenTooltipTitle={isDisabled ? (
          <TooltipTitle
            isActiveBackgroundJob={isActiveBackgroundJob}
            isBudgetCalculationInProcess={isBudgetCalculationInProcess}
            isAvailableByBudgetType={isAvailableByBudgetType}
          />
        ) : null}
      />
      {!isDisabled && <AgreementParametersConfirmationModal />}
    </div>
  );
};

AgreementParametersOperationsGroup.propTypes = {
  isEditDisabled: PropTypes.bool,
};

AgreementParametersOperationsGroup.defaultProps = {
  isEditDisabled: false,
};

export default AgreementParametersOperationsGroup;
