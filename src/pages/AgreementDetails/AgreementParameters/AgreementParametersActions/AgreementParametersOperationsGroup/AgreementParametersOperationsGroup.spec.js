import React from 'react';
import { Provider } from 'react-redux';
import Router, { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { combineReducers, configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import MockAdapter from 'axios-mock-adapter';
import { AppContextProvider } from 'AppContextProvider';
import { ngaAxios } from 'core/services/HTTPService';
import rootReducer from 'core/rootReducer';
import { budgetTypes } from 'core/contstants';
import { getBudgetDetailsPath } from 'core/configs/paths';
import renewAgreementsAction from 'features/RenewAgreements/actions';
import {
  AgreementDetailsContextProvider,
} from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import mockAgreementParametersData from 'pages/AgreementDetails/AgreementParameters/GetAgreementParameters/mockAgreementParametersData';
import {
  fireEvent, render, screen,
} from '@testing-library/react';
import getAgreementParametersUrl from 'pages/AgreementDetails/AgreementParameters/GetAgreementParameters/apiUrls';
import {
  actionButtonsText, actions, modifiers,
} from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/constants';
import AgreementParametersActionsProvider from 'pages/AgreementDetails/AgreementParameters/AgreementParametersActions/AgreementParametersActionsProvider';
import useActiveBudgetBackgroundJob
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useActiveBudgetBackgroundJob';
import useBudgetCalculationProcess
  from 'features/BudgetCalculation/hooks/useBudgetCalculationProcess';
import AgreementParametersOperationsGroup from './index';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

const abortFn = jest.fn();
global.AbortController = jest.fn(() => ({
  abort: abortFn,
}));

jest.mock('features/RenewAgreements/actions');
jest.mock('features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useActiveBudgetBackgroundJob');
jest.mock('features/BudgetCalculation/hooks/useBudgetCalculationProcess');

renewAgreementsAction.mockImplementation(() => jest.fn());

describe('AgreementDetails: AgreementParameters: AgreementParametersActions: AgreementParametersOperationsGroup:', () => {
  const mockBudgetId = mockAgreementParametersData.budget_id;
  const mockAgreementId = mockAgreementParametersData.id;
  const requestUrl = getAgreementParametersUrl(mockBudgetId, mockAgreementId);
  const route = getBudgetDetailsPath(mockBudgetId);
  const history = createMemoryHistory({ initialEntries: [route] });

  const defaultStore = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
  });

  const actionButtonSelectors = {
    [actions.activate]: actionButtonsText.activate,
    [actions.renew]: actionButtonsText.renew,
    [actions.delete]: actionButtonsText.delete,
    [actions.clone]: actionButtonsText.clone,
  };

  const actionDescriptions = {
    [actions.activate]: 'After confirmation, selected agreement will be activated.',
    [actions.renew]: 'Are you sure?',
    [actions.delete]: 'After confirmation, the agreement will be deleted.',
    [actions.clone]: ' After confirmation, the selected agreement will be cloned with its discounts.',
  };

  const getAgreementParametersActions = (store, isEditDisabled) => (
    <Provider store={store}>
      <AppContextProvider>
        <AgreementDetailsContextProvider>
          <HistoryRouter history={history}>
            <AgreementParametersActionsProvider
              getAgreementParameters={jest.fn}
              isMasterBudget={false}
            >
              <AgreementParametersOperationsGroup isEditDisabled={isEditDisabled} />
            </AgreementParametersActionsProvider>
          </HistoryRouter>
        </AgreementDetailsContextProvider>
      </AppContextProvider>
    </Provider>
  );

  const mockNgaAxios = new MockAdapter(ngaAxios);

  const mockRequestsForDefaultRender = () => {
    mockNgaAxios.onGet(requestUrl).reply(200, mockAgreementParametersData);
    jest.spyOn(Router, 'useParams').mockReturnValue({
      agreementId: mockAgreementId, budgetId: mockBudgetId,
    });
  };

  const defaultReducer = {
    agreementParameters: () => ({
      data: { status: null },
      isLoading: false,
    }),
    budgetParameters: () => ({
      data: {},
    }),
    renewAgreements: () => ({
      data: [],
      isLoading: false,
      error: {},
    }),
    activateAgreements: () => ({
      data: [],
      isLoading: false,
    }),
    deactivateAgreements: () => ({
      data: [],
      isLoading: false,
    }),
    cloneAgreement: () => ({
      data: {},
      isLoading: false,
    }),
    removeAgreements: () => ({
      data: [],
      isLoading: false,
    }),
  };

  beforeEach(() => {
    mockRequestsForDefaultRender();

    useActiveBudgetBackgroundJob.mockReturnValue({ isActiveBackgroundJob: false });
    useBudgetCalculationProcess.mockReturnValue({ isBudgetCalculationInProcess: false });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should be AgreementParametersActions in the DOM', () => {
    render(getAgreementParametersActions(defaultStore));

    expect(screen.getByTestId(modifiers.operationsGroup)).toBeInTheDocument();
  });

  describe.each([
    [actions.activate,
      actionButtonSelectors[actions.activate], actionDescriptions[actions.activate],
    ],
    [actions.renew, actionButtonSelectors[actions.renew], actionDescriptions[actions.renew]],
    [actions.delete, actionButtonSelectors[actions.delete], actionDescriptions[actions.delete]],
  ])('AgreementDetails: AgreementParameters: AgreementParametersActions: AgreementParametersOperationsGroup: %s action', (action, buttonSelector, description) => {
    test(`should open popover and display ${action} description`, async () => {
      render(getAgreementParametersActions(defaultStore));

      const openButton = document.querySelector('.actions__btn-open-popover');

      fireEvent.click(openButton);

      const actionButton = await screen.findByText(buttonSelector);

      fireEvent.click(actionButton);

      const descriptionElement = screen.getByText(description);

      expect(descriptionElement).toBeInTheDocument();
    });
  });

  test('should not be agreement parameters edit button ', async () => {
    render(getAgreementParametersActions(defaultStore, true));

    const openButton = document.querySelector('.actions__btn-open-popover');

    fireEvent.click(openButton);

    const editButton = await screen.queryByText(actionButtonsText.edit);

    expect(editButton).toBeNull();
  });

  describe('AgreementDetails: AgreementParameters: AgreementParametersActions: AgreementParametersOperationsGroup: disable', () => {
    test.each([
      [true, true, false, budgetTypes.updated],
      [true, false, true, budgetTypes.updated],
      [true, true, true, budgetTypes.updated],
      [false, false, false, budgetTypes.updated],
      [false, false, false, budgetTypes.master],
      [false, false, false, budgetTypes.frozenTraffic],
      [true, false, false, budgetTypes.frozen],
    ])('should btnOpenPopover has disabled = %p, if isActiveBackgroundJob = %p, isBudgetCalculationInProcess = %p, budgetType = %p', async (
      isDisabled, isActiveBackgroundJob, isBudgetCalculationInProcess, budgetType,
    ) => {
      const testReducer = combineReducers({
        ...defaultReducer,
        budgetParameters: () => ({
          data: { type: budgetType },
        }),
      });

      const testStore = configureStore({
        reducer: testReducer,
        middleware: getDefaultMiddleware({
          serializableCheck: false,
        }),
      });

      useActiveBudgetBackgroundJob.mockReturnValue({ isActiveBackgroundJob });
      useBudgetCalculationProcess.mockReturnValue({ isBudgetCalculationInProcess });

      render(getAgreementParametersActions(testStore));

      const openButton = document.querySelector('.actions__btn-open-popover');

      if (isDisabled) {
        expect(openButton).toBeDisabled();
      } else {
        expect(openButton).not.toBeDisabled();
      }
    });
  });
});
