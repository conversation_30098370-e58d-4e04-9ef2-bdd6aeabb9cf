import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

import { GrBriefcase } from 'react-icons/gr';
import { Typography } from '@mui/material';

import { useAppContext } from 'AppContextProvider';
import BackButton from '@nv2/nv2-pkg-js-shared-components/lib/BackButton';
import { getBudgetDetailsPath } from 'core/configs/paths';
import { HTTPService } from 'core/services';
import { useAgreementDetailsContext } from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import EntityName from 'shared/EntityParameters/EntityName';
import EntityOperators from 'shared/EntityParameters/EntityOperators';
import EntityTimeline from 'shared/EntityTimeline';
import AgreementStatusLabel from 'shared/AgreementStatusLabel';
import EntityParametersPreloader from 'shared/EntityParameters/EntityParametersPreloader';
import getBudgetParametersAction from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/actions';
import AgreementCalculation from 'pages/AgreementDetails/AgreementCalculation';
import useBudgetCalculatingProgress
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByCalculation/hooks/useBudgetCalculatingProgress';
import useBudgetBackgroundJobProgressByBudgetComponent
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useBudgetBackgroundJobProgressByBudgetComponent';
import budgetComponents from 'features/BudgetDetailsChannel/constants';
import appStyles from 'assets/styles/variables.module.scss';

import ParameterItem from 'shared/ParameterItem';
import getAgreementParametersAction from './GetAgreementParameters/actions';
import AgreementParametersActions from './AgreementParametersActions';
import AgreementCalculationStatus from './AgreementCalculationStatus';
import AgreementParametersActionsProvider from './AgreementParametersActions/AgreementParametersActionsProvider';
import { tabIndexQuery } from './constants';
import AgreementFilters from './AgreementFilters';
import AgreementIntersections from './AgreementIntersections';
import AgreementParametersProvider from './AgreementParametersProvider';
import BooleanParameterItem from './BooleanParameterItem';
import { parameterItemTextFormat } from './BooleanParameterItem/constants';
import LinkedAgreements from './LinkedAgreements';

let budgetParametersController = HTTPService.getController();
let agreementParametersController = HTTPService.getController();

const AgreementParameters = ({ setErrorPageNotFound }) => {
  const { agreementId, budgetId } = useAgreementDetailsContext();
  const { primaryColor } = useAppContext();

  const backPath = `${getBudgetDetailsPath(budgetId)}${tabIndexQuery}`;

  const dispatch = useDispatch();
  const agreementParameters = useSelector((state) => state.agreementParameters.data);
  const isAgreementParametersLoading = useSelector((state) => state.agreementParameters.isLoading);
  const budgetParameters = useSelector((state) => state.budgetParameters.data);
  const { isBudgetCalculationSuccess } = useBudgetCalculatingProgress();
  const {
    isBudgetBackgroundJobFinished,
    isBudgetBackgroundJobFailed,
  } = useBudgetBackgroundJobProgressByBudgetComponent({
    budgetComponent: budgetComponents.agreements,
  });

  const isMasterBudget = budgetParameters?.is_master;

  const showIntersections = !!agreementParameters?.intersection_types?.length && !isMasterBudget;

  const cancelGetAgreementParametersRequest = () => {
    HTTPService.cancelRequest(agreementParametersController);
  };

  const cancelGetBudgetParametersRequest = () => {
    HTTPService.cancelRequest(budgetParametersController);
  };

  const cancelRequests = () => {
    cancelGetAgreementParametersRequest();
    cancelGetBudgetParametersRequest();
  };

  const getAgreementParameters = async () => {
    cancelGetAgreementParametersRequest();
    agreementParametersController = HTTPService.getController();

    try {
      await dispatch(getAgreementParametersAction(
        agreementParametersController, budgetId, agreementId),
      );
    } catch (error) {
      if (error?.response?.status === 404) {
        setErrorPageNotFound(true);
      }
    }
  };

  const getBudgetParameters = async () => {
    cancelGetBudgetParametersRequest();
    budgetParametersController = HTTPService.getController();

    try {
      await dispatch(getBudgetParametersAction(
        budgetParametersController, budgetId),
      );
    } catch (error) {
      //
    }
  };

  const agreementParametersRender = () => (
    <AgreementParametersProvider>
      <BackButton path={backPath} />
      <div className="agreement-parameters__icon"><GrBriefcase size={18} color={primaryColor} /></div>
      <div className="entity-parameters-name__wrap">
        <EntityName name={agreementParameters.name} transitionPoint={appStyles.smallDesktopWidth} />
        <LinkedAgreements />
      </div>
      <div className="agreement-parameters__entities">
        <EntityOperators operators={agreementParameters.home_operators} />
        <EntityOperators
          operators={agreementParameters.partner_operators}
          isPartnerOperators
        />
        <EntityTimeline
          startDate={agreementParameters.start_date}
          endDate={agreementParameters.end_date}
          lastHistoricalMonth={budgetParameters.last_historical_month}
        />
        <div className="agreement-parameters__statuses">
          <ParameterItem
            label="Status"
            value={<AgreementStatusLabel statusValue={agreementParameters.status} />}
          />
          <ParameterItem
            label="Negotiator"
            value={(
              <Typography
                variant="body1"
                component="span"
                className="parameter-item__value"
              >
                {agreementParameters.negotiator?.name || '-'}
              </Typography>
      )}
          />
          {!isMasterBudget && (<AgreementCalculationStatus />)}
          <BooleanParameterItem
            label="Active"
            value={agreementParameters.is_active}
            textFormat={parameterItemTextFormat.yesNo}
          />
          <BooleanParameterItem
            label="Premium"
            value={agreementParameters.include_premium}
          />
          <BooleanParameterItem
            label="Premium in Commitments"
            value={agreementParameters.include_premium_in_commitment}
          />
          <BooleanParameterItem
            label="Satellites"
            value={agreementParameters.include_satellite}
          />
          <BooleanParameterItem
            label="Rolling"
            value={agreementParameters.is_rolling}
            textFormat={parameterItemTextFormat.yesNo}
          />
        </div>
      </div>
      <div className="agreement-parameters__buttons">
        {showIntersections && (
        <AgreementIntersections
          getAgreementParameters={getAgreementParameters}
        />
        )}
        {!isMasterBudget && (<AgreementCalculation />)}
        <AgreementFilters />
        <AgreementParametersActionsProvider
          getAgreementParameters={getAgreementParameters}
          isMasterBudget={isMasterBudget}
        >
          <AgreementParametersActions />
        </AgreementParametersActionsProvider>
      </div>
    </AgreementParametersProvider>
  );

  const init = () => {
    getAgreementParameters();
    getBudgetParameters();
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    if (isBudgetCalculationSuccess) {
      init();
    }

    return () => cancelRequests();
  }, [isBudgetCalculationSuccess]);

  useEffect(() => {
    if (isBudgetBackgroundJobFinished || isBudgetBackgroundJobFailed) {
      init();
    }

    return () => cancelRequests();
  }, [isBudgetBackgroundJobFinished, isBudgetBackgroundJobFailed]);

  return (
    <div className="agreement-parameters">
      {isAgreementParametersLoading ? <EntityParametersPreloader /> : agreementParametersRender()}
    </div>
  );
};

AgreementParameters.propTypes = {
  setErrorPageNotFound: PropTypes.func,
};
AgreementParameters.defaultProps = {
  setErrorPageNotFound: () => {},
};

export default AgreementParameters;
