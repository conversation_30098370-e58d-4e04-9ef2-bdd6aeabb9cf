import React from 'react';
import { render, screen } from '@testing-library/react';

import {
  linkedAgreementsPreviewIconWrapModifier,
} from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/LinkedAgreementsPreview/constants';

import LinkedAgreementsPreview from './LinkedAgreementsPreview';

describe('AgreementDetails: AgreementParameters: LinkedAgreements: LinkedAgreementsPreview', () => {
  const mockLinkedAgreements = [
    {
      id: 1,
      end_date: '2023-01-01',
      name: 'Agreement 1',
      start_date: '2022-01-01',
      status: 'active',
    },
    {
      id: 2,
      end_date: '2024-01-01',
      name: 'Agreement 2',
      start_date: '2023-01-01',
      status: 'inactive',
    },
  ];

  const defaultProps = {
    linkedAgreements: mockLinkedAgreements,
    currentAgreementId: '1',
    currentBudgetId: '10',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render null when the chain has only one or no agreement', () => {
    const { container } = render(
      <LinkedAgreementsPreview
        {...defaultProps}
        linkedAgreements={[mockLinkedAgreements[0]]}
      />,
    );

    expect(container.firstChild).toBeNull();
  });

  test('should render the icon wrap when there are multiple linked agreements', () => {
    render(<LinkedAgreementsPreview {...defaultProps} />);

    expect(screen.getByTestId(linkedAgreementsPreviewIconWrapModifier)).toBeInTheDocument();
  });
});
