import React from 'react';
import PropTypes from 'prop-types';

import {
  Link, TableCell, TableRow, Typography,
} from '@mui/material';

import { getAgreementDetailsPath } from 'core/configs/paths';
import AgreementStatusLabel from 'shared/AgreementStatusLabel';
import {
  linkedAgreementsPreviewTableCurrent,
  linkedAgreementsPreviewTableEmptyRow,
  linkedAgreementsPreviewTableLink,
  linkedAgreementsPreviewTableMarker,
} from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/LinkedAgreementsPreview/constants';

const LinkedAgreementRow = ({
  isCurrent,
  id,
  budgetId,
  endDate,
  name,
  startDate,
  status,
}) => {
  const path = getAgreementDetailsPath(budgetId, id);

  return (
    <>
      <TableRow className={isCurrent ? linkedAgreementsPreviewTableCurrent : ''}>
        <TableCell>
          <span className={linkedAgreementsPreviewTableMarker} />
          {isCurrent
            ? (
              <Typography
                variant="body2"
                component="span"
              >
                {name}
              </Typography>
            )
            : (
              <Link
                variant="body2"
                target="_blank"
                className={linkedAgreementsPreviewTableLink}
                data-testid={linkedAgreementsPreviewTableLink}
                href={path}
              >
                {name}
              </Link>
            )}
        </TableCell>
        <TableCell>
          {`${startDate} - ${endDate}`}
        </TableCell>
        <TableCell>
          <AgreementStatusLabel statusValue={status} />
        </TableCell>
      </TableRow>
      <TableRow className={linkedAgreementsPreviewTableEmptyRow}>
        <TableCell colSpan={3}>|</TableCell>
      </TableRow>
    </>
  );
};

LinkedAgreementRow.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  budgetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  isCurrent: PropTypes.bool,
  endDate: PropTypes.string,
  name: PropTypes.string,
  startDate: PropTypes.string,
  status: PropTypes.string,
};

LinkedAgreementRow.defaultProps = {
  isCurrent: false,
  endDate: '',
  name: '',
  startDate: '',
  status: '',
};

export default LinkedAgreementRow;
