/* stylelint-disable no-descending-specificity */

.linked-agreements-preview-table {
  border-collapse: separate !important;
  margin-bottom: 10px;
  margin-top: 10px;

  &__marker {
    display: inline-flex;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid $light-color-500;
  }

  .MuiTableCell-root {
    background-color: $dark-color-300 !important;
    border: 0 !important;
    color: $light-color-50 !important;
    font-size: 12px;
    font-weight: 600 !important;
    padding: 2px 10px !important;

    &:first-child {
      @media (max-width: $tablet-width) {
        max-width: 500px;
      }

      @media (max-width: $mobile-width) {
        max-width: 300px;
      }

      @media (max-width: $small-mobile-width) {
        max-width: 150px;
      }
    }
  }

  &__empty-row {
    .MuiTableCell-root {
      font-size: 8px !important;
      padding: 2px 13px !important;
    }

    &:last-child {
      display: none;
    }
  }

  &__current {
    .MuiTableCell-root {
      padding-top: 6px !important;
      padding-bottom: 6px !important;
      font-weight: 600 !important;
      color: var(--brand-yellow-color-500) !important;
      border: 1px solid var(--brand-yellow-color-500) !important;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-right-width: 0 !important;
      }

      &:nth-child(2) {
        border-left-width: 0 !important;
        border-right-width: 0 !important;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left-width: 0 !important;
      }
    }

    .linked-agreements-preview-table__marker {
      position: relative;
      border-color: var(--brand-yellow-color-500) !important;

      &::after {
        position: absolute;
        content: "";
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: var(--brand-yellow-color-500) !important;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  &__link {
    color: $light-color-50 !important;
    font-weight: 600 !important;

    &:hover {
      color: var(--brand-yellow-color-500) !important;
    }
  }

  .MuiTableCell-head {
    padding-top: 2px;
    padding-bottom: 2px;
    color: $light-color-500 !important;
  }
}
