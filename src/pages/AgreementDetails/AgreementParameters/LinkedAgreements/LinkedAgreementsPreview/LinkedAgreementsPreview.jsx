import React from 'react';
import PropTypes from 'prop-types';

import {
  Table,
  TableBody,
  Tooltip,
} from '@mui/material';

import LinkedAgreementsIcon
  from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/shared/LinkedAgreementsIcon';

import LinkedAgreementRow from './LinkedAgreementRow';
import LinkedAgreementsPreviewHeader from './LinkedAgreementsPreviewHeader';
import {
  linkedAgreementsPreviewIconWrapModifier,
  linkedAgreementsPreviewTableModifier,
} from './constants';

import './LinkedAgreementsPreview.scss';

const LinkedAgreementsPreview = ({
  linkedAgreements,
  currentAgreementId,
  currentBudgetId,
}) => {
  const isNoChain = linkedAgreements.length <= 1;

  if (isNoChain) {
    return null;
  }

  const tooltipContent = (
    <Table
      className={linkedAgreementsPreviewTableModifier}
    >
      <LinkedAgreementsPreviewHeader />
      <TableBody>
        {linkedAgreements.map((agreement) => (
          <LinkedAgreementRow
            key={agreement.id}
            id={agreement.id}
            budgetId={currentBudgetId}
            isCurrent={agreement.id === +currentAgreementId}
            endDate={agreement.end_date}
            name={agreement.name}
            startDate={agreement.start_date}
            status={agreement.status}
          />
        ))}
      </TableBody>
    </Table>
  );

  return (
    <Tooltip
      data-testid=""
      placement="bottom"
      arrow
      key=""
      title={tooltipContent}
    >
      <span data-testid={linkedAgreementsPreviewIconWrapModifier}>
        <LinkedAgreementsIcon />
      </span>
    </Tooltip>
  );
};

LinkedAgreementsPreview.propTypes = {
  linkedAgreements: PropTypes.instanceOf(Array),
  currentAgreementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  currentBudgetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

LinkedAgreementsPreview.defaultProps = {
  linkedAgreements: [],
};

export default LinkedAgreementsPreview;
