import React from 'react';
import {
  render, screen, act, cleanup,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';

import { HTTPService } from 'core/services';
import { useAgreementDetailsContext } from 'pages/AgreementDetails/AgreementDetailsContextProvider';

import LinkedAgreements from './LinkedAgreements';
import * as actions from './GetLinkedAgreements/actions';
import {
  linkedAgreementsPreviewIconWrapModifier,
} from './LinkedAgreementsPreview/constants';
import {
  linkedAgreementsPreloaderModifier,
} from './LinkedAgreementsPreloader/constants';

jest.mock('core/services', () => ({
  HTTPService: {
    getController: jest.fn(),
    cancelRequest: jest.fn(),
  },
}));

jest.mock('pages/AgreementDetails/AgreementDetailsContextProvider', () => ({
  useAgreementDetailsContext: jest.fn(),
}));

jest.mock('./GetLinkedAgreements/actions', () => jest.fn());

const mockStore = configureStore([]);

describe('AgreementDetails: AgreementParameters: LinkedAgreements', () => {
  const initialState = {
    linkedAgreements: {
      data: [{ id: 1, name: 'Agreement 1' }, { id: 2, name: 'Agreement 2' }],
      isLoading: false,
    },
  };

  let store;

  beforeEach(() => {
    jest.clearAllMocks();

    store = mockStore(initialState);
    HTTPService.getController.mockReturnValue('mockController');

    useAgreementDetailsContext.mockReturnValue({
      agreementId: '123',
      budgetId: '456',
    });
  });

  afterEach(cleanup);

  test('should renders the preloader when loading is true', () => {
    store = mockStore({
      linkedAgreements: {
        data: [],
        isLoading: true,
      },
    });

    render(
      <Provider store={store}>
        <LinkedAgreements />
      </Provider>,
    );

    expect(screen.getByTestId(linkedAgreementsPreloaderModifier)).toBeInTheDocument();
  });

  test('should render LinkedAgreementsPreview when loading is false', () => {
    render(
      <Provider store={store}>
        <LinkedAgreements />
      </Provider>,
    );

    expect(screen.getByTestId(linkedAgreementsPreviewIconWrapModifier)).toBeInTheDocument();
  });

  test('should call cancel and getLinkedAgreements on mount', () => {
    jest.spyOn(actions, 'default').mockResolvedValue(() => {});

    act(() => {
      render(
        <Provider store={store}>
          <LinkedAgreements />
        </Provider>,
      );
    });

    expect(HTTPService.cancelRequest).toHaveBeenCalled();
    expect(actions.default).toHaveBeenCalledWith(
      'mockController',
      '456',
      '123',
    );
  });

  test('should clean up requests on unmount', () => {
    const { unmount } = render(
      <Provider store={store}>
        <LinkedAgreements />
      </Provider>,
    );

    unmount();

    expect(HTTPService.cancelRequest).toHaveBeenCalled();
  });
});
