import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { HTTPService } from 'core/services';
import {
  useAgreementDetailsContext,
} from 'pages/AgreementDetails/AgreementDetailsContextProvider';

import LinkedAgreementsPreview
  from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/LinkedAgreementsPreview';
import LinkedAgreementsPreloader from './LinkedAgreementsPreloader';
import getLinkedAgreementsAction from './GetLinkedAgreements/actions';

let linkedAgreementsController = HTTPService.getController();

const LinkedAgreements = () => {
  const dispatch = useDispatch();

  const { agreementId, budgetId } = useAgreementDetailsContext();

  const linkedAgreements = useSelector((state) => state.linkedAgreements.data);
  const isLinkedAgreementsLoading = useSelector((state) => state.linkedAgreements.isLoading);

  const cancelGetLinkedAgreementsRequest = () => {
    HTTPService.cancelRequest(linkedAgreementsController);
  };

  const getLinkedAgreements = async () => {
    cancelGetLinkedAgreementsRequest();
    linkedAgreementsController = HTTPService.getController();

    try {
      await dispatch(getLinkedAgreementsAction(
        linkedAgreementsController, budgetId, agreementId),
      );
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    getLinkedAgreements();

    return () => cancelGetLinkedAgreementsRequest();
  }, []);

  return isLinkedAgreementsLoading
    ? <LinkedAgreementsPreloader />
    : (
      <LinkedAgreementsPreview
        linkedAgreements={linkedAgreements}
        currentAgreementId={agreementId}
        currentBudgetId={budgetId}
      />
    );
};

export default LinkedAgreements;
