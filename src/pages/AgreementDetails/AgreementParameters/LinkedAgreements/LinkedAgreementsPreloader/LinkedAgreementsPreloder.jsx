import React from 'react';

import { Skeleton } from '@mui/material';

import LinkedAgreementsIcon
  from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/shared/LinkedAgreementsIcon';

import {
  linkedAgreementsPreloaderModifier,
  linkedAgreementsPreloaderWrapModifier,
} from './constants';
import './LinkedAgreementsPreloader.scss';

const LinkedAgreementsPreloader = () => (
  <div className={linkedAgreementsPreloaderWrapModifier}>
    <LinkedAgreementsIcon />
    <Skeleton
      variant="rect"
      className={linkedAgreementsPreloaderModifier}
      data-testid={linkedAgreementsPreloaderModifier}
      animation="wave"
    />
  </div>
);

export default LinkedAgreementsPreloader;
