import {
  GET_LINKED_AGREEMENTS_REQUEST,
  GET_LINKED_AGREEMENTS_SUCCESS,
  GET_LINKED_AGREEMENTS_FAILURE,
} from './actionTypes';

import {
  getLinkedAgreementsRequest,
  getLinkedAgreementsSuccess,
  getLinkedAgreementsFailure,
} from './actionsCreators';

describe('AgreementDetails: AgreementParameters: GetLinkedAgreements: actionCreators', () => {
  test('should create action to getLinkedAgreementsRequest', () => {
    const expectedAction = {
      type: GET_LINKED_AGREEMENTS_REQUEST,
    };

    expect(getLinkedAgreementsRequest()).toEqual(expectedAction);
  });

  test('should create action to getLinkedAgreementsSuccess', () => {
    const data = '';
    const expectedAction = {
      type: GET_LINKED_AGREEMENTS_SUCCESS,
      data,
    };

    expect(getLinkedAgreementsSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getLinkedAgreementsFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_LINKED_AGREEMENTS_FAILURE,
      error,
    };

    expect(getLinkedAgreementsFailure(error)).toEqual(expectedAction);
  });
});
