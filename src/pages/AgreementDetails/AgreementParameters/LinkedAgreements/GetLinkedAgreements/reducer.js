import {
  GET_LINKED_AGREEMENTS_REQUEST,
  GET_LINKED_AGREEMENTS_SUCCESS,
  GET_LINKED_AGREEMENTS_FAILURE,
  GET_LINKED_AGREEMENTS_CANCELED,
} from './actionTypes';

const defaultState = {
  data: [],
  isLoading: false,
  error: null,
};

const getLinkedAgreementsReducer = (state = { ...defaultState }, {
  type, data, error,
}) => {
  switch (type) {
    case GET_LINKED_AGREEMENTS_REQUEST:
      return {
        ...state,
        isLoading: true,
      };
    case GET_LINKED_AGREEMENTS_SUCCESS:
      return {
        ...state,
        data,
        isLoading: false,
      };
    case GET_LINKED_AGREEMENTS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error,
      };
    case GET_LINKED_AGREEMENTS_CANCELED:
      return {
        ...state,
        error,
      };
    default:
      return state;
  }
};

export default getLinkedAgreementsReducer;
