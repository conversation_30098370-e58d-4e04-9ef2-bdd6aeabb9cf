import {
  getLinkedAgreementsRequest,
  getLinkedAgreementsSuccess,
  getLinkedAgreementsFailure,
  getLinkedAgreementsCanceled,
} from './actionsCreators';
import getLinkedAgreements from './api.service';

const getLinkedAgreementsAction = (controller, budgetId, agreementId) => async (dispatch) => {
  try {
    const { signal } = controller;

    dispatch(getLinkedAgreementsRequest());

    const { data } = await getLinkedAgreements(signal, budgetId, agreementId);

    dispatch(getLinkedAgreementsSuccess(data));

    return data;
  } catch (error) {
    if (controller.signal?.aborted) {
      dispatch(getLinkedAgreementsCanceled(error));
    } else {
      dispatch(getLinkedAgreementsFailure(error));
    }

    throw error;
  }
};

export default getLinkedAgreementsAction;
