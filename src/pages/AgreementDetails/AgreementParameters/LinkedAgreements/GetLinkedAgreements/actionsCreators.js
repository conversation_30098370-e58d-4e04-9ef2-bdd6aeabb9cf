import {
  GET_LINKED_AGREEMENTS_REQUEST,
  GET_LINKED_AGREEMENTS_SUCCESS,
  GET_LINKED_AGREEMENTS_FAILURE,
  GET_LINKED_AGREEMENTS_CANCELED,
} from './actionTypes';

export const getLinkedAgreementsRequest = () => (
  { type: GET_LINKED_AGREEMENTS_REQUEST });
export const getLinkedAgreementsSuccess = (data) => (
  { type: GET_LINKED_AGREEMENTS_SUCCESS, data });
export const getLinkedAgreementsFailure = (error) => (
  { type: GET_LINKED_AGREEMENTS_FAILURE, error });
export const getLinkedAgreementsCanceled = (error) => (
  { type: GET_LINKED_AGREEMENTS_CANCELED, error }
);
