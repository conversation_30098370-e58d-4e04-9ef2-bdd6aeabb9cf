import { ngaAxios } from 'core/services/HTTPService';
import getLinkedAgreements from './api.service';

jest.mock('core/services/HTTPService');

describe('AgreementDetails: AgreementParameters: GetLinkedAgreements', () => {
  test('should get successfully data from an API', async () => {
    const data = '';

    ngaAxios.get.mockImplementationOnce(() => Promise.resolve(data));

    await expect(getLinkedAgreements()).resolves.toEqual(data);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    ngaAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getLinkedAgreements()).rejects.toThrow(errorMessage);
  });
});
