$less-desktop: 1600px;

.agreement-parameters {
  display: flex;
  align-items: center;
  background-color: $white-color;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  flex-wrap: wrap;
  height: auto;
  row-gap: 15px;

  &__entities {
    display: flex;
    width: 100%;
    align-items: center;

    @media (max-width: $less-desktop) {
      width: 100%;
    }

    @media (max-width: $small-desktop-width) {
      flex-wrap: wrap;
      row-gap: 20px;
      column-gap: 50px;
    }
  }

  &__statuses {
    display: flex;
    align-items: center;

    @media (min-width: $less-desktop) {
      gap: 20px;
    }

    @media (max-width: $small-mobile-width) {
      column-gap: 40px;
    }

    @media (max-width: $small-tablet-width) {
      flex-wrap: wrap;
      row-gap: 20px;
      column-gap: 20px;
    }
  }

  &__icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: $shadow8;
    margin-right: 20px;
    border-radius: 4px;

    @media (max-width: $small-tablet-width) {
      display: none;
    }

    path {
      stroke: currentcolor !important;
    }
  }

  &__buttons {
    display: flex;
    margin-left: auto;
    gap: 20px;
    position: absolute;
    top: 15px;
    right: 15px;

    @media (max-width: $small-mobile-width) {
      .MuiButtonBase-root {
        min-width: 32px !important;
        min-height: 32px !important;
        width: 32px !important;
        height: 32px !important;
      }
    }
  }

  .back-button {
    margin-right: 12px !important;

    @media (max-width: $small-tablet-width) {
      width: 100%;
    }
  }

  .entity-parameters-name__wrap, .entity-timeline, .entity-parameters-operators, .agreement-parameters-item {
    margin-right: 40px !important;

    @media (max-width: $small-desktop-width) {
      margin-right: 30px !important;
    }

    @media (max-width: $small-mobile-width) {
      margin-right: 0 !important;
    }
  }

  .calculation-state {
    @media (max-width: $small-mobile-width) {
      margin-right: 0 !important;
    }
  }

  .entity-parameters-name__wrap {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    @media (max-width: $less-desktop) {
      max-width: 65%;
    }

    @media (max-width: $small-tablet-width) {
      max-width: 100%;
    }
  }
}
