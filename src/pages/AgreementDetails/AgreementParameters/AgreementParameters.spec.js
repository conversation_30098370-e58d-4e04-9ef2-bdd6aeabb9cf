import React from 'react';
import { act, render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import Router, { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { combineReducers, configureStore } from '@reduxjs/toolkit';
import MockAdapter from 'axios-mock-adapter';

import { AppContextProvider } from 'AppContextProvider';
import { ngaAxios } from 'core/services/HTTPService';
import { getBudgetDetailsPath } from 'core/configs/paths';
import {
  AgreementDetailsContextProvider,
} from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import preloaderIModifier from 'shared/EntityParameters/EntityParametersPreloader/constants';
import AgreementParameters from 'pages/AgreementDetails/AgreementParameters/AgreementParameters';
import mockAgreementParametersData from 'pages/AgreementDetails/AgreementParameters/GetAgreementParameters/mockAgreementParametersData';
import getBudgetParametersAction from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/actions';
import {
  mockLastBudgetCalculationDataWhenCalculationIsNotRunning,
} from 'features/BudgetCalculation/GetLastBudgetCalculation/mockLastBudgetCalculationData';
import mockRunBudgetCalculationData
  from 'features/BudgetCalculation/BudgetCalculationModal/BudgetCalculationRun/RunBudgetCalculation/mockRunBudgetCalculationData';
import useBudgetCalculationProgress
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByCalculation/hooks/useBudgetCalculatingProgress';
import getAgreementParametersAction
  from 'pages/AgreementDetails/AgreementParameters/GetAgreementParameters/actions';

import useBudgetBackgroundJobProgressByBudgetComponent
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useBudgetBackgroundJobProgressByBudgetComponent';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

const abortFn = jest.fn();
global.AbortController = jest.fn(() => ({
  abort: abortFn,
}));

jest.mock('features/BudgetDetailsChannel/BudgetDetailsChannelByCalculation/hooks/useBudgetCalculatingProgress');
jest.mock('pages/BudgetDetails/BudgetParameters/GetBudgetParameters/actions');
jest.mock('pages/AgreementDetails/AgreementParameters/GetAgreementParameters/actions');
jest.mock('features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/hooks/useBudgetBackgroundJobProgressByBudgetComponent', () => jest.fn());

describe('AgreementDetails: AgreementParameters', () => {
  const mockBudgetId = mockAgreementParametersData.budget_id;
  const mockAgreementId = mockAgreementParametersData.id;
  const route = getBudgetDetailsPath(mockBudgetId);
  const history = createMemoryHistory({ initialEntries: [route] });
  const getAgreementParameters = (store) => (
    <Provider store={store}>
      <AppContextProvider>
        <AgreementDetailsContextProvider>
          <HistoryRouter history={history}>
            <AgreementParameters />
          </HistoryRouter>
        </AgreementDetailsContextProvider>
      </AppContextProvider>
    </Provider>
  );

  const middleware = (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: false,
  });

  const testReducer = {
    appVariables: () => ({
      appVariables: {
        themeName: 'nextgen',
      },
      isLoading: false,
    }),
    agreementParameters: () => ({
      data: mockAgreementParametersData,
      isLoading: false,
    }),
    budgetParameters: () => ({
      data: {},
      isLoading: true,
    }),
    budgetCalculationRun: () => ({
      data: mockRunBudgetCalculationData,
      isLoading: false,
    }),
    lastBudgetCalculation: () => ({
      data: {
        created_at: mockLastBudgetCalculationDataWhenCalculationIsNotRunning.created_at,
      },
    }),
    activeBackgroundJobs: () => ({
      data: [],
    }),
    budgetDetailsChannel: () => ({
      message: '',
    }),
    renewAgreements: () => ({
      data: [],
      isLoading: false,
      error: {},
    }),
    activateAgreements: () => ({
      data: [],
      isLoading: false,
    }),
    deactivateAgreements: () => ({
      data: [],
      isLoading: false,
    }),
    cloneAgreement: () => ({
      data: {},
      isLoading: false,
    }),
    removeAgreements: () => ({
      data: [],
      isLoading: false,
    }),
    updatingAgreementParameters: () => ({
      data: [],
      isLoading: false,
    }),
    linkedAgreements: () => ({
      data: [],
      isLoading: false,
    }),
  };

  const mockNgaAxios = new MockAdapter(ngaAxios);
  const mockRequestsForDefaultRender = () => {
    getBudgetParametersAction.mockImplementation(() => jest.fn());
    getAgreementParametersAction.mockReturnValue(mockAgreementParametersData);
    useBudgetCalculationProgress.mockReturnValue({ isBudgetCalculationSuccess: false });
    useBudgetBackgroundJobProgressByBudgetComponent.mockReturnValue({
      isBudgetBackgroundJobFinished: false,
      isBudgetBackgroundJobFailed: false,
    });

    jest.spyOn(Router, 'useParams').mockReturnValue({
      agreementId: mockAgreementId, budgetId: mockBudgetId,
    });
  };

  beforeEach(() => {
    mockRequestsForDefaultRender();
  });

  afterEach(() => {
    jest.clearAllMocks();
    mockNgaAxios.reset();
  });

  test('should be mock name in the DOM', async () => {
    const store = configureStore({ reducer: testReducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    const name = screen.getByText(mockAgreementParametersData.name);

    expect(name).toBeInTheDocument();
  });

  test('should be Negotiator Name in the DOM ', async () => {
    const store = configureStore({ reducer: testReducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    const negotiatorName = screen.getByText(mockAgreementParametersData.negotiator.name);

    expect(negotiatorName).toBeInTheDocument();
  });

  test('should call getAgreementParametersAction by initialization', async () => {
    const store = configureStore({ reducer: testReducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    expect(getAgreementParametersAction).toBeCalledTimes(1);
  });

  test('should be preloader in the DOM', async () => {
    const reducer = combineReducers({
      ...testReducer,
      agreementParameters: () => ({
        data: {},
        isLoading: true,
      }),
      budgetParameters: () => ({
        data: {},
        isLoading: true,
      }),
    });
    const store = configureStore({ reducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    const preloader = screen.getByTestId(preloaderIModifier);

    expect(preloader).toBeInTheDocument();
  });

  test('should call getBudgetParametersAction when data by initialization', async () => {
    const store = configureStore({ reducer: testReducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    expect(getBudgetParametersAction).toHaveBeenCalledTimes(1);
  });

  test('should call getBudgetParametersAction and getBudgetParametersAction when isBudgetBackgroundJobFinished = true or isBudgetBackgroundJobFailed = true', async () => {
    useBudgetBackgroundJobProgressByBudgetComponent.mockReturnValue({
      isBudgetBackgroundJobFinished: true,
      isBudgetBackgroundJobFailed: false,
    });

    const store = configureStore({ reducer: testReducer, middleware });

    await act(() => render(getAgreementParameters(store)));

    expect(getAgreementParametersAction).toHaveBeenCalledTimes(2);
    expect(getBudgetParametersAction).toHaveBeenCalledTimes(2);
  });
});
