import React from 'react';
import {
  act, fireEvent, render, screen,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import {
  configureStore,
  getDefaultMiddleware,
} from '@reduxjs/toolkit';
import { createMemoryHistory } from 'history';
import { ThemeProvider } from '@mui/material/styles';
import MockAdapter from 'axios-mock-adapter';
import Router, { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';

import { AppContextProvider } from 'AppContextProvider';
import rootReducer from 'core/rootReducer';
import { getBudgetDetailsPath } from 'core/configs/paths';
import mockUseScrollLock from 'core/hooks/useScrollLock';
import { ngaAxios } from 'core/services/HTTPService';
import mockAgreementsData from 'features/GetAgreements/mockAgreementsData';
import getAgreementsUrl from 'features/GetAgreements/apiUrls';
import {
  AgreementDetailsContextProvider,
} from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import * as AgreementValuesContext
  from 'pages/AgreementDetails/AgreementValues/AgreementValuesContextProvider';
import {
  AgreementValuesContextProvider,
} from 'pages/AgreementDetails/AgreementValues/AgreementValuesContextProvider';
import budgetsUrl from 'pages/BudgetList/GetBudgets/apiUrls';
import mockBudgetsData from 'pages/BudgetList/GetBudgets/mockBudgetsData';

import AgreementsComparison from './AgreementsComparison';
import {
  agreementsComparisonModalModifier,
} from './AgreementsComparisonModal/constants';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

const abortFn = jest.fn();
global.AbortController = jest.fn(() => ({
  abort: abortFn,
}));

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

jest.mock('core/hooks/useScrollLock', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => {}),
}));

const btnTexts = {
  compare: 'Compare',
  confirm: 'Confirm',
};
const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('AgreementDetails: AgreementValues: AgreementsComparison', () => {
  const mockNgaAxios = new MockAdapter(ngaAxios);
  const mockSetAgreementsComparisonData = jest.fn();
  const mockBudgetId = mockBudgetsData[0].id;
  const mockAgreementId = mockAgreementsData.results[0].id;
  const mockBudgetDataForSelect = mockBudgetsData[1];
  const route = getBudgetDetailsPath(mockBudgetId);
  const history = createMemoryHistory({ initialEntries: [route] });
  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
  });
  const agreementsComparison = (
    <Provider store={store}>
      <AppContextProvider>
        <AgreementDetailsContextProvider>
          <AgreementValuesContextProvider>
            <HistoryRouter history={history}>
              <ThemeProvider theme={theme(currentTheme)}>
                <AgreementsComparison />
              </ThemeProvider>
            </HistoryRouter>
          </AgreementValuesContextProvider>
        </AgreementDetailsContextProvider>
      </AppContextProvider>
    </Provider>
  );

  const contextValues = {
    agreementsComparisonData: [],
    setAgreementsComparisonData: mockSetAgreementsComparisonData,
  };
  const mockRequestsForDefaultRender = () => {
    jest.spyOn(Router, 'useParams').mockReturnValue({
      budgetId: mockBudgetId,
      agreementId: mockAgreementId,
    });
    jest.spyOn(AgreementValuesContext, 'useAgreementValuesContext').mockImplementation(() => contextValues);
    mockNgaAxios.onGet(budgetsUrl).reply(200, mockBudgetsData);
    mockNgaAxios.onGet(getAgreementsUrl(mockBudgetId))
      .reply(200, mockAgreementsData);
    mockNgaAxios.onGet(getAgreementsUrl(mockBudgetDataForSelect.id))
      .reply(200, mockAgreementsData);
    mockNgaAxios.onGet().reply(200, mockAgreementsData);
  };

  beforeEach(() => {
    mockNgaAxios.reset();
    mockRequestsForDefaultRender();
  });

  const openModal = async () => {
    const compareBudgetsBtn = screen.getByText(btnTexts.compare);

    await act(() => fireEvent.click(compareBudgetsBtn));
  };

  const renderAgreementsComparison = async () => {
    await act(() => render(agreementsComparison));
  };

  test('should be "Agreements comparison Modal" in the DOM when user click on "Compare" button', async () => {
    await renderAgreementsComparison();
    await openModal();

    const agreementsComparisonModal = screen.getByTestId(agreementsComparisonModalModifier);

    expect(agreementsComparisonModal).toBeInTheDocument();
  });

  test('should be "Confirm button" in the modal was disabled', async () => {
    await renderAgreementsComparison();
    await openModal();

    const confirmBtn = screen.getByText(btnTexts.confirm);

    expect(confirmBtn).toHaveAttribute('disabled');
  });

  test('should not render component when isComparisonShown is false', async () => {
    const agreementsComparisonWithHiddenProp = (
      <Provider store={store}>
        <AppContextProvider>
          <AgreementDetailsContextProvider>
            <AgreementValuesContextProvider>
              <HistoryRouter history={history}>
                <ThemeProvider theme={theme(currentTheme)}>
                  <AgreementsComparison isComparisonShown={false} />
                </ThemeProvider>
              </HistoryRouter>
            </AgreementValuesContextProvider>
          </AgreementDetailsContextProvider>
        </AppContextProvider>
      </Provider>
    );

    await act(() => render(agreementsComparisonWithHiddenProp));

    const agreementsComparisonDiv = document.getElementById('agreements-comparison');
    expect(agreementsComparisonDiv).toHaveStyle('display: none');
  });

  test('should render component with default visibility when isComparisonShown is true', async () => {
    await renderAgreementsComparison();

    const agreementsComparisonDiv = document.getElementById('agreements-comparison');
    expect(agreementsComparisonDiv).not.toHaveStyle('display: none');
  });

  test('should render component with default visibility when isComparisonShown prop is not provided', async () => {
    const agreementsComparisonWithoutProp = (
      <Provider store={store}>
        <AppContextProvider>
          <AgreementDetailsContextProvider>
            <AgreementValuesContextProvider>
              <HistoryRouter history={history}>
                <ThemeProvider theme={theme(currentTheme)}>
                  <AgreementsComparison />
                </ThemeProvider>
              </HistoryRouter>
            </AgreementValuesContextProvider>
          </AgreementDetailsContextProvider>
        </AppContextProvider>
      </Provider>
    );

    await act(() => render(agreementsComparisonWithoutProp));

    const agreementsComparisonDiv = document.getElementById('agreements-comparison');
    expect(agreementsComparisonDiv).not.toHaveStyle('display: none');
  });

  test('should initialize with default compared agreements amount', async () => {
    await renderAgreementsComparison();

    const compareButton = screen.getByText(btnTexts.compare);
    expect(compareButton).toBeInTheDocument();

    expect(compareButton.textContent).toBe(btnTexts.compare);
  });

  test('should apply scroll lock when modal is open', async () => {
    await renderAgreementsComparison();

    expect(mockUseScrollLock).toHaveBeenCalledWith(false);

    await openModal();

    expect(mockUseScrollLock).toHaveBeenCalledWith(true);
  });

  test('should render TrafficValuesCompareButton with correct props', async () => {
    await renderAgreementsComparison();

    const compareButton = screen.getByText(btnTexts.compare);
    expect(compareButton).toBeInTheDocument();

    expect(compareButton.closest('button')).toBeInTheDocument();
  });

  test('should pass setComparedAgreementsAmount to AgreementsComparisonModal', async () => {
    await renderAgreementsComparison();
    await openModal();

    const agreementsComparisonModal = screen.getByTestId(agreementsComparisonModalModifier);
    expect(agreementsComparisonModal).toBeInTheDocument();
  });
});
