import React from 'react';
import {
  act,
  fireEvent,
  render, screen,
} from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig
  from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { AppContextProvider } from 'AppContextProvider';
import { Provider } from 'react-redux';
import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import rootReducer from 'core/rootReducer';
import { MemoryRouter } from 'react-router-dom';
import { AgreementDetailsContextProvider } from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import MockAdapter from 'axios-mock-adapter';
import { ngaAxios } from 'core/services/HTTPService';
import budgetsUrl from 'pages/BudgetList/GetBudgets/apiUrls';
import mockBudgetsData from 'pages/BudgetList/GetBudgets/mockBudgetsData';
import getAgreementsUrl from 'features/GetAgreements/apiUrls';
import mockAgreementsData from 'features/GetAgreements/mockAgreementsData';
import AgreementsComparisonModalContent from './AgreementsComparisonModalContent';

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    budgetId: '1',
    agreementId: '1',
  }),
}));

jest.mock('core/services', () => ({
  HTTPService: {
    cancelRequest: jest.fn(),
    getController: jest.fn(() => new AbortController()),
  },
}));

jest.mock('pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/GetAgreements/actions', () => jest.fn(() => ({ type: 'MOCK_ACTION' })));

jest.mock('pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/hooks/useAgreementsForActionsFilters', () => () => ({
  agreementsFilters: {},
  setAgreementsFilters: jest.fn(),
}));

jest.mock('pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/hooks/useGetAgreementsForActionsFilters', () => () => {});

jest.mock('pages/BudgetList/GetBudgets/actions', () => jest.fn(() => () => Promise.resolve()));

jest.mock('pages/AgreementDetails/AgreementParameters/AgreementFilters/hooks/useAppliedFiltersCount', () => () => 0);

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: AgreementsComparisonModalContent', () => {
  const mockNgaAxios = new MockAdapter(ngaAxios);
  const resetAgreementsComparison = jest.fn();
  const confirmComparison = jest.fn();
  const addAgreementToAgreementsComparisonModalData = jest.fn();
  const setAgreementValueInAgreementsComparisonModalData = jest.fn();
  const removeAgreementFromAgreementsComparisonModalData = jest.fn();
  const removeColorFromAgreementMarkersColors = jest.fn();
  const setLastSelectedBudgetId = jest.fn();

  const mockProps = {
    resetAgreementsComparison,
    confirmComparison,
    agreementsComparisonModalData: [
      {
        value: {
          id: 1,
          name: 'Test Agreement 1',
          budget_id: 1,
          budget_name: 'Test Budget',
        },
        markerColor: '#ff0000',
      },
      {
        value: {
          id: 2,
          name: 'Test Agreement 2',
          budget_id: 1,
          budget_name: 'Test Budget',
        },
        markerColor: '#00ff00',
      },
    ],
    setAgreementsComparisonModalData: jest.fn(),
    addAgreementToAgreementsComparisonModalData,
    setAgreementValueInAgreementsComparisonModalData,
    removeAgreementFromAgreementsComparisonModalData,
    agreementMarkersColors: ['#ff0000', '#00ff00'],
    removeColorFromAgreementMarkersColors,
    addColorToAgreementMarkersColors: jest.fn(),
    currentAgreementId: 1,
    lastSelectedBudgetId: 1,
    setLastSelectedBudgetId,
  };

  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
    preloadedState: {
      budgets: {
        data: [
          { id: 1, name: 'Test Budget 1' },
          { id: 2, name: 'Test Budget 2' },
        ],
        isLoading: false,
      },
      agreementsForModalAction: {
        data: { results: [] },
        isLoading: false,
      },
    },
  });

  const agreementsComparisonModalContent = (
    <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
      <Provider store={store}>
        <AppContextProvider>
          <AgreementDetailsContextProvider>
            <ThemeProvider theme={theme(currentTheme)}>
              <AgreementsComparisonModalContent {...mockProps} />
            </ThemeProvider>
          </AgreementDetailsContextProvider>
        </AppContextProvider>
      </Provider>
    </MemoryRouter>
  );

  beforeEach(() => {
    mockNgaAxios.reset();
    mockNgaAxios.onGet(budgetsUrl).reply(200, mockBudgetsData);
    mockNgaAxios.onGet(getAgreementsUrl(1)).reply(200, mockAgreementsData);
    mockNgaAxios.onGet().reply(200, mockAgreementsData);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render reset and confirm buttons in the DOM', async () => {
    render(agreementsComparisonModalContent);
    const confirmBtn = screen.getByRole('button', { name: /Confirm/i });
    const resetBtn = screen.getByRole('button', { name: /Reset filtration/i });
    expect(resetBtn).toBeInTheDocument();
    expect(confirmBtn).toBeInTheDocument();
  });

  test('should call resetAgreementsComparison when reset button is clicked', async () => {
    render(agreementsComparisonModalContent);
    const resetBtn = screen.getByRole('button', { name: /Reset filtration/i });
    await act(() => fireEvent.click(resetBtn));
    expect(resetAgreementsComparison).toBeCalled();
  });

  test('should call confirmComparison when confirm button is clicked', async () => {
    render(agreementsComparisonModalContent);
    const confirmBtn = screen.getByRole('button', { name: /Confirm/i });
    await act(() => fireEvent.click(confirmBtn));
    expect(confirmComparison).toBeCalled();
  });

  test('should render budget selection component', () => {
    render(agreementsComparisonModalContent);

    expect(screen.getByText('Budget')).toBeInTheDocument();
  });

  test('should render calculation status filter', () => {
    render(agreementsComparisonModalContent);

    expect(screen.getByLabelText('Calculation Status')).toBeInTheDocument();
  });

  test('should render agreement filters component', () => {
    render(agreementsComparisonModalContent);

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    expect(filtersButton).toBeInTheDocument();
  });

  test('should render transfer buttons', () => {
    render(agreementsComparisonModalContent);

    const transferDownButton = screen.getByTestId('transfer-down-button');
    const transferUpButton = screen.getByTestId('transfer-up-button');

    expect(transferDownButton).toBeInTheDocument();
    expect(transferUpButton).toBeInTheDocument();
  });

  test('should disable transfer down button when no items selected in top section', () => {
    render(agreementsComparisonModalContent);

    const transferDownButton = screen.getByTestId('transfer-down-button');
    expect(transferDownButton).toBeDisabled();
  });

  test('should disable transfer up button when only current agreement is selected', () => {
    render(agreementsComparisonModalContent);

    const transferUpButton = screen.getByTestId('transfer-up-button');
    expect(transferUpButton).toBeDisabled();
  });

  test('should disable transfer down button when maximum agreements reached', () => {
    const propsWithMaxAgreements = {
      ...mockProps,
      agreementsComparisonModalData: [
        { value: { id: 1, name: 'Agreement 1' }, markerColor: '#ff0000' },
        { value: { id: 2, name: 'Agreement 2' }, markerColor: '#00ff00' },
        { value: { id: 3, name: 'Agreement 3' }, markerColor: '#0000ff' },
        { value: { id: 4, name: 'Agreement 4' }, markerColor: '#ffff00' },
        { value: { id: 5, name: 'Agreement 5' }, markerColor: '#ff00ff' },
      ],
    };

    const agreementsComparisonModalContentWithMax = (
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={store}>
          <AppContextProvider>
            <AgreementDetailsContextProvider>
              <ThemeProvider theme={theme(currentTheme)}>
                <AgreementsComparisonModalContent {...propsWithMaxAgreements} />
              </ThemeProvider>
            </AgreementDetailsContextProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>
    );

    render(agreementsComparisonModalContentWithMax);

    const transferDownButton = screen.getByTestId('transfer-down-button');
    expect(transferDownButton).toBeDisabled();
  });

  test('should render agreements transfer table', () => {
    render(agreementsComparisonModalContent);

    expect(screen.getByText('Test Agreement 1')).toBeInTheDocument();
    expect(screen.getByText('Test Agreement 2')).toBeInTheDocument();
  });

  test('should handle budget change', async () => {
    render(agreementsComparisonModalContent);

    const budgetSelect = screen.getByDisplayValue('Test Budget 1');
    await act(() => fireEvent.change(budgetSelect, { target: { value: '2' } }));

    expect(budgetSelect).toBeInTheDocument();
  });

  test('should handle calculation status change', async () => {
    render(agreementsComparisonModalContent);

    const calculationStatusInput = screen.getByLabelText('Calculation Status');
    expect(calculationStatusInput).toBeInTheDocument();

    await act(() => fireEvent.click(calculationStatusInput));
  });

  test('should show loading state when initial loading', () => {
    const propsWithLoading = {
      ...mockProps,
      agreementsComparisonModalData: [],
    };

    const storeWithLoadingState = configureStore({
      reducer: rootReducer,
      middleware: getDefaultMiddleware({
        serializableCheck: false,
      }),
      preloadedState: {
        budgets: {
          data: [],
          isLoading: true,
        },
        agreementsForModalAction: {
          data: { results: [] },
          isLoading: true,
        },
      },
    });

    const agreementsComparisonModalContentWithLoading = (
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={storeWithLoadingState}>
          <AppContextProvider>
            <AgreementDetailsContextProvider>
              <ThemeProvider theme={theme(currentTheme)}>
                <AgreementsComparisonModalContent {...propsWithLoading} />
              </ThemeProvider>
            </AgreementDetailsContextProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>
    );

    render(agreementsComparisonModalContentWithLoading);

    expect(document.getElementById('agreement-compare-filter')).toBeInTheDocument();
  });
});
