import { useState } from 'react';

const useAgreementsComparisonModalData = () => {
  const [agreementsComparisonModalData, setAgreementsComparisonModalData] = useState(
    [],
  );

  const addAgreementToAgreementsComparisonModalData = (markerColor) => {
    setAgreementsComparisonModalData((state) => [
      ...state,
      {
        id: Date.now(),
        markerColor,
        value: {},
        readOnly: false,
      },
    ]);
  };

  const removeAgreementFromAgreementsComparisonModalData = (rowIndex) => {
    const newAgreementsComparisonModalData = [...agreementsComparisonModalData];

    newAgreementsComparisonModalData.splice(rowIndex, 1);
    setAgreementsComparisonModalData(newAgreementsComparisonModalData);
  };

  const setAgreementValueInAgreementsComparisonModalData = ({
    rowIndex,
    agreementId,
    agreementName,
    partnerOperators,
    homeOperators,
    agreementData,
  }) => {
    setAgreementsComparisonModalData((state) => {
      const newAgreementsComparisonModalData = [...state];

      if (agreementData) {
        newAgreementsComparisonModalData[rowIndex].value = {
          ...agreementData,
          budget_name: newAgreementsComparisonModalData[rowIndex].value.budget_name,
        };
      } else {
        newAgreementsComparisonModalData[rowIndex].value.id = agreementId;
        newAgreementsComparisonModalData[rowIndex].value.name = agreementName;
        newAgreementsComparisonModalData[rowIndex].value.partner_operators = partnerOperators;
        newAgreementsComparisonModalData[rowIndex].value.home_operators = homeOperators;
      }

      return newAgreementsComparisonModalData;
    });
  };

  const clearAgreementValueInAgreementsComparisonModalData = (rowIndex) => {
    setAgreementsComparisonModalData((state) => {
      const newAgreementsComparisonModalData = [...state];
      newAgreementsComparisonModalData[rowIndex].value = {
        ...newAgreementsComparisonModalData[rowIndex].value,
        id: null,
        name: null,
      };
      return newAgreementsComparisonModalData;
    });
  };

  const setBudgetValueToAgreementsComparisonModalData = (rowIndex, budgetValue) => {
    setAgreementsComparisonModalData((state) => {
      const newAgreementsComparisonModalData = [...state];
      newAgreementsComparisonModalData[rowIndex].value = {
        ...newAgreementsComparisonModalData[rowIndex].value,
        budget_id: budgetValue.id,
        budget_name: budgetValue.name,
      };
      return newAgreementsComparisonModalData;
    });
  };

  const resetAgreementsComparisonModalData = ({
    currentAgreementId,
    currentBudgetId,
    currentAgreementName,
    partnerOperators,
    homeOperators,
    budgets,
    markerColor,
    agreementParametersData,
  }) => {
    const currentBudget = budgets.find(({ id }) => +id === +currentBudgetId);

    const defaultAgreementsComparisonModalData = [{
      id: Date.now(),
      markerColor,
      value: {
        id: currentAgreementId,
        budget_id: currentBudgetId,
        budget_name: currentBudget?.name,
        agreement_id: currentAgreementId,
        name: currentAgreementName,
        status: agreementParametersData?.status || 'DRAFT',
        calculation_status: agreementParametersData?.calculation_status || 'NOT_APPLIED',
        start_date: agreementParametersData?.start_date,
        end_date: agreementParametersData?.end_date,
        is_active: agreementParametersData?.is_active || false,
        negotiator: agreementParametersData?.negotiator,
        home_operators: homeOperators,
        partner_operators: partnerOperators,
        partner_countries: agreementParametersData?.partner_countries || [],
        updated_at: agreementParametersData?.updated_at,
        applied_at: agreementParametersData?.applied_at,
      },
      readOnly: true,
    }];

    setAgreementsComparisonModalData(defaultAgreementsComparisonModalData);
  };

  return {
    agreementsComparisonModalData,
    resetAgreementsComparisonModalData,
    addAgreementToAgreementsComparisonModalData,
    setAgreementsComparisonModalData,
    removeAgreementFromAgreementsComparisonModalData,
    setAgreementValueInAgreementsComparisonModalData,
    clearAgreementValueInAgreementsComparisonModalData,
    setBudgetValueToAgreementsComparisonModalData,
  };
};

export default useAgreementsComparisonModalData;
