import React from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import {
  MuiTableContext,
} from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';

import { HTTPService } from 'core/services';
import getIds from 'core/utilities/getIds';
import getFormattedSortFieldForRequest from 'core/utilities/getFormattedSortFieldForRequest';
import getAgreementsAction from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/GetAgreements/actions';
import FormPreloader from 'shared/FormPreloader';
import AgreementsTable from './AgreementTable';

const { MuiTableProvider } = MuiTableContext;
let agreementsController = new AbortController();

export const AgreementsTableProvider = ({
  budgetValue,
  selectedAgreementIds,
  setSelectedAgreementIds,
  agreementsFilters,
  isNeedToUpdateTable,
  excludedAgreementIds = [],
  selectionLimit = 5,
  refreshTrigger,
  isLoading,
  isFiltersInitialized = true,
  ...rest
}) => {
  const dispatch = useDispatch();
  const agreementsFiltersData = useSelector((state) => state.agreementsForActionsFilters.data);

  const agreements = useSelector(
    (state) => state.agreementsForModalAction.data?.results) || [];

  const filteredAgreements = agreements.filter(
    (agreement) => !excludedAgreementIds.includes(agreement.id),
  );

  const dispatchGetAgreementsAction = (tableParams) => {
    HTTPService.cancelRequest(agreementsController);
    agreementsController = HTTPService.getController();

    const dateFilters = {
      start_date_min: agreementsFiltersData?.start_date_min,
      start_date_max: agreementsFiltersData?.start_date_max,
      end_date_min: agreementsFiltersData?.end_date_min,
      end_date_max: agreementsFiltersData?.end_date_max,
    };

    const params = {
      ...tableParams,
      budget_id: budgetValue,
      home_operators: getIds(agreementsFiltersData?.home_operators || []),
      ...agreementsFilters,
      ...dateFilters,
    };

    dispatch(getAgreementsAction(agreementsController, params));
  };

  const onChangeAgreements = ({ page, pageSize }, sort, search) => {
    const params = {
      page,
      page_size: pageSize,
      search,
      sort_field: getFormattedSortFieldForRequest(sort),
    };

    dispatchGetAgreementsAction(params);
  };

  return (
    <>
      <FormPreloader isLoading={isLoading} withProgress={false} />
      <MuiTableProvider
        onChange={onChangeAgreements}
        defaultPagination={{ page: 1, pageSize: 5 }}
      >
        <AgreementsTable
          dispatchGetAgreementsAction={dispatchGetAgreementsAction}
          agreementsFilters={agreementsFilters}
          selectedAgreementIds={selectedAgreementIds}
          setSelectedAgreementIds={setSelectedAgreementIds}
          isNeedToUpdateTable={isNeedToUpdateTable}
          rows={filteredAgreements}
          selectionLimit={selectionLimit}
          excludedAgreementIds={excludedAgreementIds}
          refreshTrigger={refreshTrigger}
          isFiltersInitialized={isFiltersInitialized}
          {...rest}
        />
      </MuiTableProvider>
    </>
  );
};

AgreementsTableProvider.propTypes = {
  budgetValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  selectedAgreementIds: PropTypes.instanceOf(Array),
  setSelectedAgreementIds: PropTypes.func,
  agreementsFilters: PropTypes.instanceOf(Object),
  isNeedToUpdateTable: PropTypes.bool,
  isLoading: PropTypes.bool.isRequired,
  excludedAgreementIds: PropTypes.instanceOf(Array),
  selectionLimit: PropTypes.number,
  refreshTrigger: PropTypes.number,
  isFiltersInitialized: PropTypes.bool,
};

AgreementsTableProvider.defaultProps = {
  budgetValue: '',
  selectedAgreementIds: [],
  setSelectedAgreementIds: () => {},
  agreementsFilters: {},
  isNeedToUpdateTable: false,
  excludedAgreementIds: [],
  selectionLimit: 5,
  refreshTrigger: 0,
  isFiltersInitialized: true,
};

export default AgreementsTableProvider;
