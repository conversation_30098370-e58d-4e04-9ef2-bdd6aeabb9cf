import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import MuiTable, {
  MuiTableConstants, MuiTableContext,
} from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';

import { useAppContext } from 'AppContextProvider';
import getFormattedSortFieldForRequest from 'core/utilities/getFormattedSortFieldForRequest';

import BudgetComponentsTableHeader
  from 'pages/BudgetDetails/BudgetItems/shared/ModalToCopyBudgetComponentsFromAnotherBudget/BudgetComponentsTableHeader';
import useDeviceResolution from 'core/hooks/useDeviceResolution';
import {
  maxTableHeight,
  rowHeight,
  agreementsTableWrapModifier,
  agreementsTableRowModifier,
  agreementsTableCellModifier, maxTableHeightMobile,
} from './constants';

import {
  agreementsTableConfig,
  noDataConfig,
} from './configs';

import './AgreementsTable.scss';

const { firstPage } = MuiTableConstants;
const { useMuiTableContext } = MuiTableContext;

const agreementComparisonPageSizeOptions = [5, 10, 50, 100];

const AgreementsTable = ({
  dispatchGetAgreementsAction,
  agreementsFilters,
  selectedAgreementIds,
  setSelectedAgreementIds,
  isNeedToUpdateTable,
  rows = [],
  selectionLimit = 5,
  excludedAgreementIds = [],
  refreshTrigger,
  isFiltersInitialized = true,
  ...rest
}) => {
  const { columns } = rest;
  const {
    pagination, setPagination, searchValue, sort,
  } = useMuiTableContext();

  const { isMobile } = useDeviceResolution();

  const isAgreementsLoading = useSelector(
    (state) => state.agreementsForModalAction.isLoading);
  const agreementsCount = useSelector(
    (state) => state.agreementsForModalAction.data?.count || 0);

  const { primaryColor, getBrandColors } = useAppContext();

  const getAgreements = () => {
    if (pagination.page !== firstPage) {
      setPagination({ page: firstPage, pageSize: pagination.pageSize });
    }

    const params = {
      page: firstPage,
      page_size: pagination.pageSize,
      search: searchValue,
      sort_field: getFormattedSortFieldForRequest(sort[0]),
    };

    dispatchGetAgreementsAction(params);
  };

  const onSelectionChange = (selectedRows) => {
    if (selectedRows.length <= selectionLimit) {
      setSelectedAgreementIds(selectedRows);
    }
  };

  useEffect(() => {
    if (isFiltersInitialized) {
      getAgreements();
    }
  }, [agreementsFilters, isFiltersInitialized]);

  useEffect(() => {
    if (isNeedToUpdateTable) {
      getAgreements();
    }
  }, [isNeedToUpdateTable]);

  useEffect(() => {
    if (refreshTrigger > 0) {
      setPagination({ page: firstPage, pageSize: pagination.pageSize });
    }
  }, [refreshTrigger, setPagination, pagination.pageSize]);

  const getCheckboxProps = (row) => {
    const isChecked = selectedAgreementIds.includes(row.id);
    const isExcluded = excludedAgreementIds.includes(row.id);
    const isSelectionLimitReached = selectedAgreementIds.length >= selectionLimit;

    return {
      disabled: isExcluded || (isSelectionLimitReached && !isChecked) || selectionLimit === 0,
    };
  };

  return (
    <div
      data-testid={agreementsTableWrapModifier}
      className={agreementsTableWrapModifier}
    >
      <BudgetComponentsTableHeader
        budgetComponentsQuantity={agreementsCount}
        title="Available Agreements"
      />
      <MuiTable
        {...rest}
        rows={rows}
        columns={columns || agreementsTableConfig}
        loading={isAgreementsLoading}
        primaryColor={primaryColor}
        getCurrentThemeColors={getBrandColors}
        checkboxSelection
        rowSelectionModel={selectedAgreementIds}
        onRowSelectionModelChange={onSelectionChange}
        isVisibleSearchInput
        disableVirtualization
        noDataConfig={noDataConfig(primaryColor)}
        rowCount={agreementsCount}
        pageSizeOptions={agreementComparisonPageSizeOptions}
        maxTableHeight={isMobile ? maxTableHeightMobile : maxTableHeight}
        getRowClassName={() => agreementsTableRowModifier}
        getRowHeight={() => rowHeight}
        getCellClassName={() => agreementsTableCellModifier}
        getCheckboxProps={getCheckboxProps}
        checkboxSelectionVisibleOnly={false}
        disableSelectAllCheckbox
      />
    </div>
  );
};

AgreementsTable.propTypes = {
  dispatchGetAgreementsAction: PropTypes.func,
  selectedAgreementIds: PropTypes.instanceOf(Array),
  setSelectedAgreementIds: PropTypes.func,
  agreementsFilters: PropTypes.instanceOf(Object),
  isNeedToUpdateTable: PropTypes.bool,
  rows: PropTypes.instanceOf(Array),
  selectionLimit: PropTypes.number,
  excludedAgreementIds: PropTypes.instanceOf(Array),
  refreshTrigger: PropTypes.number,
  isFiltersInitialized: PropTypes.bool,
};

AgreementsTable.defaultProps = {
  dispatchGetAgreementsAction: () => {},
  selectedAgreementIds: [],
  setSelectedAgreementIds: () => {},
  agreementsFilters: {},
  isNeedToUpdateTable: false,
  rows: [],
  selectionLimit: 5,
  excludedAgreementIds: [],
  refreshTrigger: 0,
  isFiltersInitialized: true,
};

export default AgreementsTable;
