import React from 'react';
import {
  render,
  screen,
  waitFor,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import { ThemeProvider } from '@mui/material/styles';
import { MemoryRouter } from 'react-router-dom';
import MockAdapter from 'axios-mock-adapter';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';

import { AppContextProvider } from 'AppContextProvider';
import rootReducer from 'core/rootReducer';
import { ngaAxios } from 'core/services/HTTPService';
import { HTTPService } from 'core/services';
import mockAgreementsData from 'features/GetAgreements/mockAgreementsData';
import getAgreementsAction from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/GetAgreements/actions';

import { AgreementsTableProvider } from './AgreementTableProvider';

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

jest.mock('core/services', () => ({
  HTTPService: {
    cancelRequest: jest.fn(),
    getController: jest.fn(() => ({ signal: {} })),
  },
}));

jest.mock('core/hooks/useDeviceResolution', () => () => ({
  isMobile: false,
}));

jest.mock('pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/GetAgreements/actions', () => jest.fn(() => ({ type: 'MOCK_GET_AGREEMENTS' })));

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: AgreementTableProvider', () => {
  const mockNgaAxios = new MockAdapter(ngaAxios);
  const mockSetSelectedAgreementIds = jest.fn();

  const mockBudgetParameters = {
    home_operators: [
      { id: 1, name: 'Home Op 1' },
      { id: 2, name: 'Home Op 2' },
    ],
  };

  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
    preloadedState: {
      budgetParameters: {
        data: mockBudgetParameters,
      },
      agreementsForModalAction: {
        data: mockAgreementsData,
        isLoading: false,
      },
      agreementsForActionsFilters: {
        data: {
          home_operators: [
            { id: 1, name: 'Home Op 1' },
            { id: 2, name: 'Home Op 2' },
          ],
          start_date_min: '2023-01-01',
          start_date_max: '2023-12-31',
          end_date_min: '2023-01-01',
          end_date_max: '2023-12-31',
        },
        isLoading: false,
      },
    },
  });

  const mockProps = {
    budgetValue: 1,
    selectedAgreementIds: [],
    setSelectedAgreementIds: mockSetSelectedAgreementIds,
    agreementsFilters: {},
    isNeedToUpdateTable: false,
    excludedAgreementIds: [],
    selectionLimit: 5,
    refreshTrigger: 0,
    isLoading: false,
  };

  const renderComponent = (props = {}) => {
    const finalProps = { ...mockProps, ...props };

    return render(
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={store}>
          <AppContextProvider>
            <ThemeProvider theme={theme(currentTheme)}>
              <AgreementsTableProvider {...finalProps} />
            </ThemeProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>,
    );
  };

  beforeEach(() => {
    mockNgaAxios.reset();
    jest.clearAllMocks();
  });

  test('should render FormPreloader when loading', () => {
    renderComponent({ isLoading: true });

    expect(screen.getByTestId('form-preloader')).toBeInTheDocument();
  });

  test('should not render FormPreloader when not loading', () => {
    renderComponent({ isLoading: false });

    expect(screen.queryByTestId('form-preloader')).not.toBeInTheDocument();
  });

  test('should render AgreementTable component', () => {
    renderComponent();

    expect(screen.getByTestId('agreements-table-in-modal__wrap')).toBeInTheDocument();
  });

  test('should filter out excluded agreements from rows', () => {
    const excludedIds = [1, 2];
    renderComponent({ excludedAgreementIds: excludedIds });

    expect(screen.getByTestId('agreements-table-in-modal__wrap')).toBeInTheDocument();
  });

  test('should dispatch get agreements action with correct parameters', async () => {
    renderComponent({
      budgetValue: 2,
      agreementsFilters: {
        status: 'ACTIVE',
        calculation_statuses: ['APPLIED'],
      },
    });

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalled();
    });
  });

  test('should handle onChange callback from MuiTableProvider', async () => {
    renderComponent();

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalled();
    });
  });

  test('should include home operators in request parameters', async () => {
    renderComponent();

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          budget_id: 1,
          home_operators: [1, 2],
        }),
      );
    });
  });

  test('should include agreements filters in request parameters', async () => {
    const customFilters = {
      status: 'ACTIVE',
      calculation_statuses: ['APPLIED', 'NOT_APPLIED'],
    };

    renderComponent({ agreementsFilters: customFilters });

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining(customFilters),
      );
    });
  });

  test('should handle date range filters', async () => {
    const filtersWithDateRange = {
      start_date: '2023-01-01',
      end_date: '2023-12-31',
    };

    renderComponent({ agreementsFilters: filtersWithDateRange });

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalled();
    });
  });

  test('should cancel previous request when new request is made', async () => {
    renderComponent();

    await waitFor(() => {
      expect(HTTPService.cancelRequest).toHaveBeenCalled();
    });
  });

  test('should handle empty budget parameters', () => {
    const storeWithEmptyBudgetParams = configureStore({
      reducer: rootReducer,
      middleware: getDefaultMiddleware({
        serializableCheck: false,
      }),
      preloadedState: {
        budgetParameters: {
          data: { home_operators: [] },
        },
        agreementsForModalAction: {
          data: mockAgreementsData,
          isLoading: false,
        },
      },
    });

    render(
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={storeWithEmptyBudgetParams}>
          <AppContextProvider>
            <ThemeProvider theme={theme(currentTheme)}>
              <AgreementsTableProvider {...mockProps} />
            </ThemeProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>,
    );

    expect(screen.getByTestId('agreements-table-in-modal__wrap')).toBeInTheDocument();
  });

  test('should handle missing agreements data', () => {
    const storeWithNoAgreements = configureStore({
      reducer: rootReducer,
      middleware: getDefaultMiddleware({
        serializableCheck: false,
      }),
      preloadedState: {
        budgetParameters: {
          data: mockBudgetParameters,
        },
        agreementsForModalAction: {
          data: null,
          isLoading: false,
        },
      },
    });

    render(
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={storeWithNoAgreements}>
          <AppContextProvider>
            <ThemeProvider theme={theme(currentTheme)}>
              <AgreementsTableProvider {...mockProps} />
            </ThemeProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>,
    );

    expect(screen.getByTestId('agreements-table-in-modal__wrap')).toBeInTheDocument();
  });

  test('should pass all props to AgreementTable', () => {
    const customProps = {
      columns: [{ field: 'custom', headerName: 'Custom' }],
      Actions: () => <div>Custom Actions</div>,
    };

    renderComponent(customProps);

    expect(screen.getByTestId('agreements-table-in-modal__wrap')).toBeInTheDocument();
  });

  test('should handle undefined budgetValue', async () => {
    renderComponent({ budgetValue: undefined });

    await waitFor(() => {
      expect(getAgreementsAction).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          budget_id: '',
        }),
      );
    });
  });
});
