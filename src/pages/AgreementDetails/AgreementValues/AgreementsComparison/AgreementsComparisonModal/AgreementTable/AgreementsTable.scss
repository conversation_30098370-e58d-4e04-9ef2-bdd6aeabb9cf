@import "src/pages/BudgetDetails/BudgetItems/variables";

.agreements-table-in-modal {
  &__wrap {
    display: flex;
    flex-direction: column;

    .MuiDataGrid-cell {
      min-height: 47px !important;
    }

    .interactions {
      &__actions {
        @media (max-width: $tablet-width) {
          width: 100%;
        }

        @media (max-width: $small-tablet-width) {
          flex-direction: column;
          gap: 15px;
        }
      }

      &__actions-selections {
        @media (max-width: $tablet-width) {
          position: absolute;
          margin-top: -45px;
          margin-left: 260px !important;
          width: 250px;
        }

        @media (max-width: $small-mobile-width) {
          position: unset;
          margin: 0 !important;
          width: 100%;
        }
      }

      @media (max-width: $tablet-width) {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start !important;
      }

      .search {
        @media (max-width: $tablet-width) {
          flex: 1;
          min-width: 200px;
        }

        @media (max-width: $small-mobile-width) {
          width: 100%;
        }
      }
    }

    .MuiDataGrid-root {
      .MuiDataGrid-overlayWrapper {
        @media (max-width: $mobile-width) {
          height: 240px !important;

          .table__no-data-wrap {
            height: 240px !important;
          }
        }
      }
    }
  }
}
