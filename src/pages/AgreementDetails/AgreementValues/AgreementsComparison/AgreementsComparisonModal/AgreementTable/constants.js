export const maxTableHeight = '560px';
export const maxTableHeightMobile = '320px';

export const agreementsTableModifier = 'agreements-table-in-modal';
export const agreementsTableWrapModifier = `${agreementsTableModifier}__wrap`;
export const agreementsTableRowModifier = `${agreementsTableModifier}__row`;
export const agreementsTableCellModifier = `${agreementsTableModifier}__cell`;

export const agreementFields = {
  id: 'id',
  name: 'name',
  homeOperators: 'home_operators',
  partnerOperators: 'partner_operators',
  partnerCountries: 'partner_countries',
  status: 'status',
  startDate: 'start_date',
  endDate: 'end_date',
  isActive: 'is_active',
};

export const rowHeight = 48;
