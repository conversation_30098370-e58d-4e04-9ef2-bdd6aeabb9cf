.agreements-comparison-modal {
  box-shadow: $shadow16;
  width: 100%;

  .custom-modal__title-wrap {
    margin-bottom: 25px;
  }

  .custom-modal__content {
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .budget-compare-filters-wrap {
    display: flex;
    gap: 15px;

    @media (max-width: $tablet-width) {
      gap: 15px;
      width: 100%;
      flex-wrap: wrap;
    }

    @media (max-width: $small-mobile-width) {
      gap: 20px;
    }

    .modal-to-copy-budget-components-from-another-budget-budget-selection {
      padding: 0 !important;
      min-width: 265px;
      max-width: 265px;

      @media (max-width: $tablet-width) {
        flex: 1;
        max-width: 100%;
        width: 100%;
      }

      @media (max-width: $tablet-width) {
        min-width: 285px;
      }
    }

    .calculation-status {
      min-width: 300px;
      max-width: 300px;

      @media (max-width: $tablet-width) {
        flex: 1;
        max-width: 100%;
        width: 100%;
      }

      @media (max-width: $tablet-width) {
        min-width: 200px;
      }
    }

    .agreements-compare-table-filters__btn {
      @media (max-width: $tablet-width) {
        align-self: flex-start;
        margin-top: 0;
      }
    }
  }

  &__actions {
    display: flex;
    margin-top: 30px !important;
    gap: 20px;
    flex-shrink: 0;

    &-btn {
      width: 127px !important;
    }

    &-wrap {
      display: flex;
      gap: 20px;
    }
  }

  @media (max-width: $small-mobile-width) {
    padding: 20px;
  }
}
