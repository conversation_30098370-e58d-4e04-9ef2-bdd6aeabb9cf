import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';

import { cloneDeep, isEmpty } from 'lodash';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';

import getBudgetsAction from 'pages/BudgetList/GetBudgets/actions';
import { HTTPService } from 'core/services';
import {
  useAgreementDetailsContext,
} from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import defaultComparedAgreementsAmount
  from 'pages/AgreementDetails/AgreementValues/AgreementsComparison/constants';
import AgreementsComparisonModalContent from './AgreementsComparisonModalContent';
import {
  agreementsComparisonModalModifier, defaultAgreementMarkersColors,
} from './constants';
import useAgreementsComparisonModalData from './useAgreementsComparisonModalData';
import useAgreementMarkersColors from './useAgreementMarkersColors';

import './AgreementsComparisonModal.scss';

const controller = {
  budgets: null,
};

const AgreementsComparisonModal = ({
  closeModal,
  isModalOpen,
  setComparedAgreementsAmount,
}) => {
  const {
    budgetId: currentBudgetId,
    agreementId: currentAgreementId,
    agreementsComparisonData,
    setAgreementsComparisonData,
  } = useAgreementDetailsContext();
  const {
    agreementsComparisonModalData,
    resetAgreementsComparisonModalData,
    setAgreementsComparisonModalData,
  } = useAgreementsComparisonModalData();
  const {
    resetAgreementMarkerColors,
    removeColorFromAgreementMarkersColors,
    addColorToAgreementMarkersColors,
    agreementMarkersColors,
  } = useAgreementMarkersColors();

  const dispatch = useDispatch();

  const budgets = useSelector((state) => state.budgets.data);
  const agreementParameters = useSelector((state) => state.agreementParameters.data);

  const cancelGetBudgetsRequest = () => {
    if (controller.budgets) {
      HTTPService.cancelRequest(controller.budgets);
    }
  };

  const cancelRequests = () => {
    cancelGetBudgetsRequest();
  };

  const getBudgets = () => {
    cancelGetBudgetsRequest();

    controller.budgets = HTTPService.getController();

    return dispatch(getBudgetsAction(controller.budgets));
  };

  const closeBudgetsComparisonModal = () => {
    closeModal();

    cancelRequests();
  };

  const cancelUnappliedAgreementsForComparison = () => {
    closeBudgetsComparisonModal();

    setAgreementsComparisonModalData([...agreementsComparisonData]);
  };

  const compareAgreements = () => {
    const newAgreementsComparisonData = cloneDeep(agreementsComparisonModalData);

    setAgreementsComparisonData(newAgreementsComparisonData);
    setComparedAgreementsAmount(agreementsComparisonModalData.filter(
      ({ value }) => !isEmpty(value)).length);

    closeBudgetsComparisonModal();
  };

  const resetAgreementsComparison = () => {
    setAgreementsComparisonData([]);
    setComparedAgreementsAmount(defaultComparedAgreementsAmount);
    setAgreementsComparisonModalData([]);
    closeBudgetsComparisonModal();
  };

  const initDefaultAgreementComparisonModalData = () => {
    if (!agreementsComparisonData.length) {
      resetAgreementMarkerColors();
      resetAgreementsComparisonModalData({
        currentAgreementId,
        currentBudgetId,
        partnerOperators: agreementParameters.partner_operators,
        homeOperators: agreementParameters.home_operators,
        budgets,
        currentAgreementName: agreementParameters.name,
        markerColor: defaultAgreementMarkersColors[0],
        agreementParametersData: agreementParameters,
      });
      removeColorFromAgreementMarkersColors();
    }
  };

  const initBudgetsAutocomplete = async () => {
    await getBudgets();
  };

  useEffect(() => () => cancelRequests(), []);

  useEffect(() => {
    initDefaultAgreementComparisonModalData();
  }, [agreementParameters, budgets]);

  useEffect(() => {
    if (isModalOpen) {
      initBudgetsAutocomplete();
    }
  }, [isModalOpen]);

  return (
    <CustomModal
      isOpen={isModalOpen}
      handleOpen={cancelUnappliedAgreementsForComparison}
      title="Compare agreements"
      dataTestid={agreementsComparisonModalModifier}
      modalClass={agreementsComparisonModalModifier}
      showButtons={false}
      onClickCancel={cancelUnappliedAgreementsForComparison}
      showConfirmButtonPreloader={false}
    >
      <AgreementsComparisonModalContent
        agreementsComparisonModalData={agreementsComparisonModalData}
        setAgreementsComparisonModalData={setAgreementsComparisonModalData}
        agreementMarkersColors={agreementMarkersColors}
        removeColorFromAgreementMarkersColors={removeColorFromAgreementMarkersColors}
        addColorToAgreementMarkersColors={addColorToAgreementMarkersColors}
        currentAgreementId={currentAgreementId}
        resetAgreementsComparison={resetAgreementsComparison}
        confirmComparison={compareAgreements}
      />
    </CustomModal>
  );
};

AgreementsComparisonModal.propTypes = {
  closeModal: PropTypes.func,
  isModalOpen: PropTypes.bool,
  setComparedAgreementsAmount: PropTypes.func,
};

AgreementsComparisonModal.defaultProps = {
  closeModal: () => {},
  isModalOpen: false,
  setComparedAgreementsAmount: () => {},
};

export default AgreementsComparisonModal;
