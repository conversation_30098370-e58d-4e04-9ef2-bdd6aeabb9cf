import { act, renderHook } from '@testing-library/react';

import useAgreementsComparisonModalData from './useAgreementsComparisonModalData';

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: useAgreementsComparisonModalData', () => {
  const testAgreementName = 'Agreement';
  const testAgreementId = 1;
  const testBudgetId = 1;
  const testBudgetName = 'Budget';
  const testMarkerColor = '#ffffff';
  const testAgreementData = {
    testMarkerColor,
    value: {
      budget_id: testBudgetId,
      budget_name: 'Test',
      id: testAgreementId,
      name: testAgreementName,
    },
    readOnly: true,
  };
  const testAgreementsComparisonModalData = [
    testAgreementData,
  ];

  const resetAgreementsComparisonModalData = (result) => {
    act(() => result.current.resetAgreementsComparisonModalData({
      currentAgreementId: testAgreementId,
      currentBudgetId: testBudgetId,
      currentAgreementName: testAgreementName,
      partnerOperators: [],
      homeOperators: [],
      budgets: [{ id: testBudgetId, name: testBudgetName }],
      markerColor: testMarkerColor,
      agreementParametersData: {
        status: 'DRAFT',
        calculation_status: 'NOT_APPLIED',
        start_date: undefined,
        end_date: undefined,
        is_active: false,
        negotiator: undefined,
        partner_countries: [],
        updated_at: undefined,
        applied_at: undefined,
      },
    }));
  };

  test('should agreementsComparisonModalData to be default agreements comparison modal data', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    expect(result.current.agreementsComparisonModalData).toStrictEqual([]);
  });

  test('should set new data to agreementsComparisonModalData', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.setAgreementsComparisonModalData(testAgreementsComparisonModalData));

    expect(result.current.agreementsComparisonModalData).toBe(testAgreementsComparisonModalData);
  });

  test('should add agreement to agreementsComparisonModalData', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.addAgreementToAgreementsComparisonModalData(testMarkerColor));

    expect(result.current.agreementsComparisonModalData[0].value).toBeInstanceOf(Object);
    expect(result.current.agreementsComparisonModalData[0].markerColor).toBe(testMarkerColor);
  });

  test('should reset agreementsComparisonModalData to default initial agreements data', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    resetAgreementsComparisonModalData(result);

    expect(result.current.agreementsComparisonModalData[0].value).toStrictEqual({
      budget_id: testBudgetId,
      budget_name: testBudgetName,
      id: testAgreementId,
      agreement_id: testAgreementId,
      name: testAgreementName,
      status: 'DRAFT',
      calculation_status: 'NOT_APPLIED',
      start_date: undefined,
      end_date: undefined,
      is_active: false,
      negotiator: undefined,
      home_operators: [],
      partner_operators: [],
      partner_countries: [],
      updated_at: undefined,
      applied_at: undefined,
    });
  });

  test('should set agreement value in agreements comparison modal data', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);
    const newAgreementId = '4';
    const newAgreementName = 'Test';

    resetAgreementsComparisonModalData(result);

    act(() => result.current.setAgreementValueInAgreementsComparisonModalData({
      rowIndex: 0,
      agreementId: newAgreementId,
      agreementName: newAgreementName,
    },
    ));

    expect(result.current.agreementsComparisonModalData[0].value).toEqual(expect.objectContaining({
      id: newAgreementId,
      name: newAgreementName,
    }));
  });

  test('should clear agreement value in agreements comparison modal data', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    resetAgreementsComparisonModalData(result);

    act(() => result.current.clearAgreementValueInAgreementsComparisonModalData(0));

    expect(result.current.agreementsComparisonModalData[0].value).toEqual(expect.objectContaining({
      id: null,
      name: null,
    }));
  });

  test('should set budget value in agreements comparison modal data', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);
    const testRowIndex = 0;
    const testBudgetValue = {
      id: 4,
      name: 'Test',
    };

    resetAgreementsComparisonModalData(result);

    act(() => result.current.setBudgetValueToAgreementsComparisonModalData(
      testRowIndex, testBudgetValue,
    ));

    expect(result.current.agreementsComparisonModalData[0].value)
      .toEqual(expect.objectContaining({
        budget_id: testBudgetValue.id,
        budget_name: testBudgetValue.name,
      }));
  });

  test('should remove agreement from agreementsComparisonModalData', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    resetAgreementsComparisonModalData(result);

    act(() => result.current.removeAgreementFromAgreementsComparisonModalData(0));

    expect(result.current.agreementsComparisonModalData).toStrictEqual([]);
  });

  test('should handle removing agreement from middle of array', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.addAgreementToAgreementsComparisonModalData('#ff0000'));
    act(() => result.current.addAgreementToAgreementsComparisonModalData('#00ff00'));
    act(() => result.current.addAgreementToAgreementsComparisonModalData('#0000ff'));

    expect(result.current.agreementsComparisonModalData).toHaveLength(3);

    act(() => result.current.removeAgreementFromAgreementsComparisonModalData(1));

    expect(result.current.agreementsComparisonModalData).toHaveLength(2);
    expect(result.current.agreementsComparisonModalData[0].markerColor).toBe('#ff0000');
    expect(result.current.agreementsComparisonModalData[1].markerColor).toBe('#0000ff');
  });

  test('should handle removing agreement with invalid index', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    resetAgreementsComparisonModalData(result);

    act(() => result.current.removeAgreementFromAgreementsComparisonModalData(10));

    expect(result.current.agreementsComparisonModalData).toHaveLength(1);
  });

  test('should set agreement value with agreementData parameter', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);
    const agreementData = {
      id: 5,
      name: 'Full Agreement Data',
      status: 'ACTIVE',
      calculation_status: 'APPLIED',
      partner_operators: [{ id: 1, name: 'Partner 1' }],
      home_operators: [{ id: 2, name: 'Home 1' }],
    };

    resetAgreementsComparisonModalData(result);

    act(() => result.current.setAgreementValueInAgreementsComparisonModalData({
      rowIndex: 0,
      agreementData,
    }));

    expect(result.current.agreementsComparisonModalData[0].value).toEqual(
      expect.objectContaining({
        id: 5,
        name: 'Full Agreement Data',
        status: 'ACTIVE',
        calculation_status: 'APPLIED',
        partner_operators: [{ id: 1, name: 'Partner 1' }],
        home_operators: [{ id: 2, name: 'Home 1' }],
        budget_name: testBudgetName,
      }),
    );
  });

  test('should set agreement value with individual parameters', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);
    const partnerOperators = [{ id: 1, name: 'Partner Op' }];
    const homeOperators = [{ id: 2, name: 'Home Op' }];

    resetAgreementsComparisonModalData(result);

    act(() => result.current.setAgreementValueInAgreementsComparisonModalData({
      rowIndex: 0,
      agreementId: 99,
      agreementName: 'Individual Params Agreement',
      partnerOperators,
      homeOperators,
    }));

    expect(result.current.agreementsComparisonModalData[0].value).toEqual(
      expect.objectContaining({
        id: 99,
        name: 'Individual Params Agreement',
        partner_operators: partnerOperators,
        home_operators: homeOperators,
      }),
    );
  });

  test('should reset with missing budget in budgets array', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.resetAgreementsComparisonModalData({
      currentAgreementId: testAgreementId,
      currentBudgetId: 999,
      currentAgreementName: testAgreementName,
      partnerOperators: [],
      homeOperators: [],
      budgets: [{ id: testBudgetId, name: testBudgetName }],
      markerColor: testMarkerColor,
      agreementParametersData: {},
    }));

    expect(result.current.agreementsComparisonModalData[0].value.budget_name).toBeUndefined();
  });

  test('should handle reset with empty budgets array', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.resetAgreementsComparisonModalData({
      currentAgreementId: testAgreementId,
      currentBudgetId: testBudgetId,
      currentAgreementName: testAgreementName,
      partnerOperators: [],
      homeOperators: [],
      budgets: [],
      markerColor: testMarkerColor,
      agreementParametersData: {},
    }));

    expect(result.current.agreementsComparisonModalData[0].value.budget_name).toBeUndefined();
  });

  test('should handle reset with null/undefined agreementParametersData', () => {
    const { result } = renderHook(useAgreementsComparisonModalData);

    act(() => result.current.resetAgreementsComparisonModalData({
      currentAgreementId: testAgreementId,
      currentBudgetId: testBudgetId,
      currentAgreementName: testAgreementName,
      partnerOperators: [],
      homeOperators: [],
      budgets: [{ id: testBudgetId, name: testBudgetName }],
      markerColor: testMarkerColor,
      agreementParametersData: null,
    }));

    const agreementValue = result.current.agreementsComparisonModalData[0].value;
    expect(agreementValue.status).toBe('DRAFT');
    expect(agreementValue.calculation_status).toBe('NOT_APPLIED');
    expect(agreementValue.is_active).toBe(false);
  });
});
