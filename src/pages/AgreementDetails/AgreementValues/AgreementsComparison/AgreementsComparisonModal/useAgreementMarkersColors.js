import { useState } from 'react';

import {
  defaultAgreementMarkersColors,
} from './constants';

const useAgreementMarkersColors = () => {
  const [agreementMarkersColors, setAgreementMarkersColors] = useState(
    defaultAgreementMarkersColors,
  );

  const resetAgreementMarkerColors = () => {
    setAgreementMarkersColors(defaultAgreementMarkersColors);
  };

  const removeColorFromAgreementMarkersColors = () => {
    setAgreementMarkersColors((state) => {
      const newAgreementMarkersColors = [...state];

      newAgreementMarkersColors.shift();

      return newAgreementMarkersColors;
    });
  };

  const addColorToAgreementMarkersColors = (newColor) => {
    setAgreementMarkersColors((state) => [...state, newColor]);
  };

  return {
    agreementMarkersColors,
    removeColorFromAgreementMarkersColors,
    resetAgreementMarkerColors,
    addColorToAgreementMarkersColors,
  };
};

export default useAgreementMarkersColors;
