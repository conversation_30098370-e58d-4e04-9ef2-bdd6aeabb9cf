import { act, renderHook } from '@testing-library/react';

import useAgreementMarkersColors from './useAgreementMarkersColors';
import {
  defaultAgreementMarkersColors,
} from './constants';

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: useAgreementMarkersColors', () => {
  test('should agreementMarkersColor to be default agreement markers colors data', () => {
    const { result } = renderHook(useAgreementMarkersColors);

    expect(result.current.agreementMarkersColors).toBe(defaultAgreementMarkersColors);
  });

  test('should remove marker color from agreement markers colors data', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const newDefaultAgreementMarkersColor = [...defaultAgreementMarkersColors];
    newDefaultAgreementMarkersColor.shift();

    act(() => result.current.removeColorFromAgreementMarkersColors());

    expect(result.current.agreementMarkersColors).toStrictEqual(newDefaultAgreementMarkersColor);
  });

  test('should reset agreement markers colors data to default agreement markers colors data', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const newDefaultAgreementMarkersColor = [...defaultAgreementMarkersColors];
    newDefaultAgreementMarkersColor.shift();

    act(() => result.current.removeColorFromAgreementMarkersColors());
    act(() => result.current.resetAgreementMarkerColors());

    expect(result.current.agreementMarkersColors).toBe(defaultAgreementMarkersColors);
  });

  test('should add markers color to agreement markers colors data', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const newMarkerColor = 'ffffff';

    act(() => result.current.addColorToAgreementMarkersColors(newMarkerColor));

    expect(result.current.agreementMarkersColors).toStrictEqual([
      ...defaultAgreementMarkersColors,
      newMarkerColor,
    ]);
  });

  test('should handle removing color when array is empty', () => {
    const { result } = renderHook(useAgreementMarkersColors);

    defaultAgreementMarkersColors.forEach(() => {
      act(() => result.current.removeColorFromAgreementMarkersColors());
    });

    expect(result.current.agreementMarkersColors).toHaveLength(0);

    expect(() => {
      act(() => result.current.removeColorFromAgreementMarkersColors());
    }).not.toThrow();

    expect(result.current.agreementMarkersColors).toHaveLength(0);
  });

  test('should handle adding multiple colors', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const newColors = ['#111111', '#222222', '#333333'];

    newColors.forEach((color) => {
      act(() => result.current.addColorToAgreementMarkersColors(color));
    });

    expect(result.current.agreementMarkersColors).toStrictEqual([
      ...defaultAgreementMarkersColors,
      ...newColors,
    ]);
  });

  test('should handle adding null or undefined color', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const initialLength = result.current.agreementMarkersColors.length;

    act(() => result.current.addColorToAgreementMarkersColors(null));
    expect(result.current.agreementMarkersColors).toHaveLength(initialLength + 1);
    expect(result.current.agreementMarkersColors[initialLength]).toBeNull();

    act(() => result.current.addColorToAgreementMarkersColors(undefined));
    expect(result.current.agreementMarkersColors).toHaveLength(initialLength + 2);
    expect(result.current.agreementMarkersColors[initialLength + 1]).toBeUndefined();
  });

  test('should handle adding empty string color', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const initialLength = result.current.agreementMarkersColors.length;

    act(() => result.current.addColorToAgreementMarkersColors(''));

    expect(result.current.agreementMarkersColors).toHaveLength(initialLength + 1);
    expect(result.current.agreementMarkersColors[initialLength]).toBe('');
  });

  test('should handle adding duplicate colors', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const duplicateColor = defaultAgreementMarkersColors[0];
    const initialLength = result.current.agreementMarkersColors.length;

    act(() => result.current.addColorToAgreementMarkersColors(duplicateColor));

    expect(result.current.agreementMarkersColors).toHaveLength(initialLength + 1);
    expect(result.current.agreementMarkersColors[initialLength]).toBe(duplicateColor);
  });

  test('should maintain state independence between multiple hook instances', () => {
    const { result: result1 } = renderHook(useAgreementMarkersColors);
    const { result: result2 } = renderHook(useAgreementMarkersColors);

    expect(result1.current.agreementMarkersColors).toStrictEqual(defaultAgreementMarkersColors);
    expect(result2.current.agreementMarkersColors).toStrictEqual(defaultAgreementMarkersColors);

    act(() => result1.current.removeColorFromAgreementMarkersColors());

    expect(result1.current.agreementMarkersColors).toHaveLength(
      defaultAgreementMarkersColors.length - 1,
    );
    expect(result2.current.agreementMarkersColors).toStrictEqual(defaultAgreementMarkersColors);
  });

  test('should handle reset after multiple operations', () => {
    const { result } = renderHook(useAgreementMarkersColors);

    act(() => result.current.removeColorFromAgreementMarkersColors());
    act(() => result.current.removeColorFromAgreementMarkersColors());
    act(() => result.current.addColorToAgreementMarkersColors('#custom1'));
    act(() => result.current.addColorToAgreementMarkersColors('#custom2'));

    expect(result.current.agreementMarkersColors).not.toStrictEqual(defaultAgreementMarkersColors);

    act(() => result.current.resetAgreementMarkerColors());

    expect(result.current.agreementMarkersColors).toBe(defaultAgreementMarkersColors);
  });

  test('should handle removing all colors and then resetting', () => {
    const { result } = renderHook(useAgreementMarkersColors);

    while (result.current.agreementMarkersColors.length > 0) {
      act(() => result.current.removeColorFromAgreementMarkersColors());
    }

    expect(result.current.agreementMarkersColors).toHaveLength(0);

    act(() => result.current.resetAgreementMarkerColors());

    expect(result.current.agreementMarkersColors).toBe(defaultAgreementMarkersColors);
    expect(result.current.agreementMarkersColors).toHaveLength(
      defaultAgreementMarkersColors.length,
    );
  });

  test('should verify removeColorFromAgreementMarkersColors removes from beginning (shift)', () => {
    const { result } = renderHook(useAgreementMarkersColors);
    const firstColor = defaultAgreementMarkersColors[0];
    const secondColor = defaultAgreementMarkersColors[1];

    act(() => result.current.removeColorFromAgreementMarkersColors());

    expect(result.current.agreementMarkersColors[0]).toBe(secondColor);
    expect(result.current.agreementMarkersColors).not.toContain(firstColor);
    expect(result.current.agreementMarkersColors).toHaveLength(
      defaultAgreementMarkersColors.length - 1,
    );
  });
});
