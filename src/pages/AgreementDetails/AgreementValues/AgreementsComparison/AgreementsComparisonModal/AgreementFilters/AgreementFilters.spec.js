import React from 'react';
import {
  act,
  fireEvent,
  render,
  screen,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { MemoryRouter } from 'react-router-dom';

import rootReducer from 'core/rootReducer';
import { AppContextProvider } from 'AppContextProvider';
import { AgreementDetailsContextProvider } from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import AgreementFilters from './AgreementFilters';
import { defaultAgreementGlobalFilters } from './AgreementFiltersModal/constants';

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    budgetId: '1',
    agreementId: '1',
  }),
}));

jest.mock('pages/AgreementDetails/AgreementParameters/AgreementFilters/hooks/useAppliedFiltersCount', () => () => 2);

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: AgreementFilters', () => {
  const mockSetAgreementsFilters = jest.fn();
  const mockAgreementsFilters = {
    home_operators: [1, 2],
    partner_operators: [3, 4],
    calculation_statuses: ['APPLIED'],
  };

  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
    preloadedState: {
      agreementsForActionsFilters: {
        isLoading: false,
        data: {
          start_date_min: '2023-01-01',
          start_date_max: '2023-12-31',
          end_date_min: '2023-01-01',
          end_date_max: '2023-12-31',
          home_operators: [],
          partner_operators: [],
          statuses: [],
          negotiators: [],
        },
      },
      agreementsForModalAction: {
        isLoading: false,
      },
    },
  });

  const mockProps = {
    isReadOnly: false,
    agreementsFilters: mockAgreementsFilters,
    setAgreementsFilters: mockSetAgreementsFilters,
    initialAgreementsFilters: defaultAgreementGlobalFilters,
  };

  const renderComponent = (props = {}) => {
    const finalProps = { ...mockProps, ...props };

    return render(
      <div id="agreement-compare-filter">
        <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
          <Provider store={store}>
            <AppContextProvider>
              <AgreementDetailsContextProvider>
                <ThemeProvider theme={theme(currentTheme)}>
                  <AgreementFilters {...finalProps} />
                </ThemeProvider>
              </AgreementDetailsContextProvider>
            </AppContextProvider>
          </Provider>
        </MemoryRouter>
      </div>,
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render filters button', () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    expect(filtersButton).toBeInTheDocument();
  });

  test('should display applied filters count', () => {
    renderComponent();

    expect(screen.getByText('2')).toBeInTheDocument();
  });

  test('should open filters modal when button is clicked', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    expect(screen.getByText('Filters')).toBeInTheDocument();
  });

  test('should close filters modal when close is called', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    expect(screen.getByText('Filters')).toBeInTheDocument();

    const modal = screen.getByTestId('agreements-compare-table-filters__modal');
    await act(() => fireEvent.keyDown(modal, { key: 'Escape' }));
  });

  test('should apply filters when apply button is clicked', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const applyButton = screen.getByRole('button', { name: /confirm/i });
    await act(() => fireEvent.click(applyButton));

    expect(mockSetAgreementsFilters).toHaveBeenCalledWith(mockAgreementsFilters);
  });

  test('should reset filters when reset button is clicked', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const resetButton = screen.getByRole('button', { name: /reset filtration/i });
    await act(() => fireEvent.click(resetButton));

    expect(mockSetAgreementsFilters).toHaveBeenCalledWith(defaultAgreementGlobalFilters);
  });

  test('should handle readonly mode', () => {
    renderComponent({ isReadOnly: true });

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    expect(filtersButton).toHaveClass('left');
  });

  test('should use custom initial filters', async () => {
    const customInitialFilters = {
      home_operators: [5, 6],
      partner_operators: [7, 8],
    };

    renderComponent({ initialAgreementsFilters: customInitialFilters });

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const resetButton = screen.getByRole('button', { name: /reset filtration/i });
    await act(() => fireEvent.click(resetButton));

    expect(mockSetAgreementsFilters).toHaveBeenCalledWith(customInitialFilters);
  });

  test('should update modal filters when modal opens', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    expect(screen.getByText('Filters')).toBeInTheDocument();
  });

  test('should handle undefined initial filters', async () => {
    renderComponent({ initialAgreementsFilters: undefined });

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const resetButton = screen.getByRole('button', { name: /reset filtration/i });
    await act(() => fireEvent.click(resetButton));

    expect(mockSetAgreementsFilters).toHaveBeenCalledWith(defaultAgreementGlobalFilters);
  });

  test('should render with correct container element', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const modal = screen.getByTestId('agreements-compare-table-filters__modal');
    expect(modal).toBeInTheDocument();
  });

  test('should have invisible backdrop', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const modal = screen.getByTestId('agreements-compare-table-filters__modal');
    expect(modal).toBeInTheDocument();

    const backdrop = modal.querySelector('.MuiBackdrop-root');
    if (backdrop) {
      expect(backdrop).toHaveStyle('background-color: transparent');
    }
  });

  test('should not show default modal buttons', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    const modal = screen.getByTestId('agreements-compare-table-filters__modal');
    expect(modal.querySelector('.custom-modal__buttons')).not.toBeInTheDocument();
  });

  test('should handle filter changes in modal content', async () => {
    renderComponent();

    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await act(() => fireEvent.click(filtersButton));

    expect(screen.getByText('Filters')).toBeInTheDocument();

    const applyButton = screen.getByRole('button', { name: /confirm/i });
    expect(applyButton).toBeInTheDocument();
  });
});
