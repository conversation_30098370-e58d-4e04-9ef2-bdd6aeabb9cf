$above-small-mobile-width: 660px;

.agreements-compare-table-filters {
  &__btn {
    position: relative !important;
  }

  &__modal {
    max-height: 80vh;
    width: 490px;
    padding: 20px !important;

    .custom-modal__content {
      height: auto !important;
    }

    .time-picker-wrapper {
      .ant-picker {
        width: 100% !important;
      }
    }

    @media (max-width: $small-mobile-width) {
      width: 350px;
    }
  }
}

#agreement-compare-filter .custom-modal {
  position: absolute !important;
  left: unset !important;
  bottom: unset !important;
  top: 170px !important;
  right: 50px !important;
  z-index: 9999 !important;

  .MuiPaper-root {
    margin: 0 !important;
    box-shadow:
      0 5px 5px -3px rgb(0 0 0 / 20%),
      0 8px 10px 1px rgb(0 0 0 / 14%),
      0 3px 14px 2px rgb(0 0 0 / 12%);
  }

  @media (max-width: $tablet-width) {
    top: 245px !important;
  }

  @media (max-width: $above-small-mobile-width) {
    top: 300px !important;
  }

  @media (max-width: $small-mobile-width) {
    top: 325px !important;
    right: 20px !important;
  }
}
