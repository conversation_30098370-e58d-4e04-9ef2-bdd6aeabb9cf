import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';
import FiltersButton from 'shared/FiltersButton';
import useAppliedFiltersCount from 'pages/AgreementDetails/AgreementParameters/AgreementFilters/hooks/useAppliedFiltersCount';
import AvailableAgreementsFiltersModalContent from './AgreementFiltersModal/AvailableAgreementsFiltersModalContent/AvailableAgreementsFiltersModalContent';
import {
  defaultAgreementGlobalFilters,
  agreementCompareTableFiltersBtnOpenModalModifier,
  agreementsCompareTableFiltersModalModifier,
} from './AgreementFiltersModal/constants';

const AgreementFilters = ({
  isReadOnly,
  agreementsFilters,
  setAgreementsFilters,
  initialAgreementsFilters,
}) => {
  const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);
  const [modalFilters, setModalFilters] = useState(
    initialAgreementsFilters || defaultAgreementGlobalFilters,
  );
  const filtersButtonClassName = `agreements-compare-table-filters__btn ${agreementCompareTableFiltersBtnOpenModalModifier} ${
    isReadOnly ? 'left' : ''
  }`;

  useEffect(() => {
    if (isFiltersModalOpen) {
      setModalFilters(agreementsFilters);
    }
  }, [isFiltersModalOpen, agreementsFilters]);

  const openFiltersModal = () => {
    setIsFiltersModalOpen(true);
  };
  const closeFiltersModal = () => {
    setIsFiltersModalOpen(false);
  };

  const applyFilters = () => {
    setAgreementsFilters(modalFilters);
    closeFiltersModal();
  };

  const resetFiltration = () => {
    setModalFilters(initialAgreementsFilters || defaultAgreementGlobalFilters);
    setAgreementsFilters(initialAgreementsFilters || defaultAgreementGlobalFilters);
    closeFiltersModal();
  };

  const appliedFiltersCount = useAppliedFiltersCount(agreementsFilters);

  return (
    <>
      <FiltersButton
        filtersCounter={appliedFiltersCount}
        className={filtersButtonClassName}
        openFiltersModal={openFiltersModal}
      />
      <CustomModal
        isOpen={isFiltersModalOpen}
        title="Filters"
        showButtons={false}
        handleOpen={setIsFiltersModalOpen}
        modalClass={agreementsCompareTableFiltersModalModifier}
        dataTestid={agreementsCompareTableFiltersModalModifier}
        container={() => document.getElementById('agreement-compare-filter')}
        BackdropProps={{ invisible: true }}
      >
        <AvailableAgreementsFiltersModalContent
          closeModal={closeFiltersModal}
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          onApplyFilters={applyFilters}
          onResetFilters={resetFiltration}
        />
      </CustomModal>
    </>
  );
};

AgreementFilters.propTypes = {
  isReadOnly: PropTypes.bool,
  agreementsFilters: PropTypes.instanceOf(Object).isRequired,
  setAgreementsFilters: PropTypes.func.isRequired,
  initialAgreementsFilters: PropTypes.instanceOf(Object),
};

AgreementFilters.defaultProps = {
  isReadOnly: false,
  initialAgreementsFilters: defaultAgreementGlobalFilters,
};

export default AgreementFilters;
