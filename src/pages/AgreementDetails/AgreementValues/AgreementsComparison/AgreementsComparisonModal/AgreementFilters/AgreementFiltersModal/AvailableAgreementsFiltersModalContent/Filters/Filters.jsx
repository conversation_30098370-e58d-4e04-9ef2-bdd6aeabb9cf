import React from 'react';
import PropTypes from 'prop-types';
import { FilterTitle, Calendar, MultipleAutocomplete } from 'shared/TableFilters';
import useAgreementStatusesWithColors
  from 'shared/AgreementStatusLabel/hooks/useAgreementStatusesWithColors';
import { agreementsFiltersFields } from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableFilters/constants';
import { useSelector } from 'react-redux';
import { agreementsCompareTableFiltersModalContentBlockModifier, agreementsCompareTableFiltersModalContentRowModifier } from '../../constants';

const Filters = ({ modalFilters, setModalFilters, agreementsFiltersKey = 'agreementsForActionsFilters' }) => {
  const agreementsFiltersData = useSelector((state) => state[agreementsFiltersKey].data);
  const agreementStatusesWithColors = useAgreementStatusesWithColors();

  return (
    <>
      <div className={`${agreementsCompareTableFiltersModalContentRowModifier} calendar`}>
        <span className={agreementsCompareTableFiltersModalContentBlockModifier}>
          <Calendar
            modalFilters={modalFilters}
            setModalFilters={setModalFilters}
            startDateField={agreementsFiltersFields.startDateMin}
            endDateField={agreementsFiltersFields.startDateMax}
            title="Start Date"
            minDate={agreementsFiltersData?.[agreementsFiltersFields.startDateMin]}
            maxDate={agreementsFiltersData?.[agreementsFiltersFields.startDateMax]}
          />
        </span>
        <span className={agreementsCompareTableFiltersModalContentBlockModifier}>
          <Calendar
            modalFilters={modalFilters}
            setModalFilters={setModalFilters}
            startDateField={agreementsFiltersFields.endDateMin}
            endDateField={agreementsFiltersFields.endDateMax}
            title="End Date"
            minDate={agreementsFiltersData?.[agreementsFiltersFields.endDateMin]}
            maxDate={agreementsFiltersData?.[agreementsFiltersFields.endDateMax]}
          />
        </span>
      </div>
      <div className={agreementsCompareTableFiltersModalContentRowModifier}>
        <FilterTitle title="Status" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={agreementsFiltersFields.statuses}
          availableOptions={
            agreementsFiltersData?.[agreementsFiltersFields.statuses] || []
          }
          optionsConfig={agreementStatusesWithColors}
          titleKey="label"
        />
      </div>
      <div className={agreementsCompareTableFiltersModalContentRowModifier}>
        <FilterTitle title="Negotiator" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={agreementsFiltersFields.negotiators}
          availableOptions={
            agreementsFiltersData?.[agreementsFiltersFields.negotiators]?.map(({ id }) => id) || []
          }
          optionsConfig={agreementsFiltersData?.[agreementsFiltersFields.negotiators] || []}
          titleKey="name"
          valueKey="id"
        />
      </div>
    </>
  );
};

Filters.propTypes = {
  modalFilters: PropTypes.instanceOf(Object).isRequired,
  setModalFilters: PropTypes.func.isRequired,
  agreementsFiltersKey: PropTypes.string,
};

Filters.defaultProps = {
  agreementsFiltersKey: 'agreementsForActionsFilters',
};

export default Filters;
