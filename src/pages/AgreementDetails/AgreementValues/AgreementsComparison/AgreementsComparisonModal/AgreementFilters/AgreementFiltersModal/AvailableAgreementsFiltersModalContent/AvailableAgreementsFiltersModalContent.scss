.agreements-compare-table-filters-modal-content {
  height: 100%;

  .calendar {
    .range-picker,
    .ant-picker-range {
      width: 200px !important;

      @media (max-width: $small-mobile-width) {
        width: 100% !important;
      }
    }
  }

  &__row {
    margin-bottom: 24px;

    &.active {
      margin-bottom: 16px !important;

      .MuiFormGroup-root {
        flex-direction: row;
      }

      .MuiFormControlLabel-label {
        font-size: 12px !important;
      }

      .filter-title {
        margin-bottom: 2px !important;
      }
    }

    @media (max-width: $small-mobile-width) {
      margin-right: 20px;
    }
  }

  &__btn {
    width: 530px;

    @media (max-width: $tablet-width) {
      width: 100%;
    }
  }

  &__block {
    display: inline-flex;
    flex-direction: column;

    &:first-child {
      margin-right: 20px;

      @media (max-width: $small-mobile-width) {
        margin-right: 0;
        margin-bottom: 20px;
      }
    }
  }

  &__actions {
    display: flex;
    margin-top: 20px !important;

    &-btn {
      width: 127px !important;
    }

    &-wrap {
      display: flex;
      gap: 20px;
    }

    @media (max-width: $small-mobile-width) {
      flex-direction: column-reverse;
      row-gap: 10px;
      align-items: flex-start;
      border-top: 1px dashed $light-color-300;
    }
  }

  .autocomplete {
    width: 100%;
  }

  .light-button {
    margin-left: auto !important;

    @media (max-width: $small-mobile-width) {
      margin-left: 0 !important;
    }
  }

  .scrollbar-container {
    max-height: 430px;

    @media (max-width: $small-mobile-width) {
      max-height: 200px !important;
    }
  }
}
