import React from 'react';
import PropTypes from 'prop-types';
import PerfectScrollbar from 'react-perfect-scrollbar';
import './AvailableAgreementsFiltersModalContent.scss';
import Actions from './Actions';
import Filters from './Filters/Filters';
import { agreementsCompareTableFiltersModalContentModifier } from '../constants';

const AvailableAgreementsFiltersModalContent = ({
  closeModal, modalFilters, setModalFilters, onApplyFilters, onResetFilters,
}) => (
  <div className={agreementsCompareTableFiltersModalContentModifier}>
    <PerfectScrollbar options={{ suppressScrollX: true }}>
      <Filters
        modalFilters={modalFilters}
        setModalFilters={setModalFilters}
      />
    </PerfectScrollbar>
    <Actions
      modalFilters={modalFilters}
      onApplyFilters={onApplyFilters}
      onResetFilters={onResetFilters}
      closeModal={closeModal}
    />
  </div>
);

AvailableAgreementsFiltersModalContent.propTypes = {
  closeModal: PropTypes.func,
  modalFilters: PropTypes.instanceOf(Object).isRequired,
  setModalFilters: PropTypes.func.isRequired,
  onApplyFilters: PropTypes.func.isRequired,
  onResetFilters: PropTypes.func.isRequired,
};

AvailableAgreementsFiltersModalContent.defaultProps = {
  closeModal: () => {},
};

export default AvailableAgreementsFiltersModalContent;
