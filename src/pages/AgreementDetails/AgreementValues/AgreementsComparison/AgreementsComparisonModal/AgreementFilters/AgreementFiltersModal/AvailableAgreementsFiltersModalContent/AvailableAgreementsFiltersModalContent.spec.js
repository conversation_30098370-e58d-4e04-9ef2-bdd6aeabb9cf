import React from 'react';
import {
  act,
  fireEvent,
  render,
  screen,
} from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { MemoryRouter } from 'react-router-dom';

import rootReducer from 'core/rootReducer';
import { AppContextProvider } from 'AppContextProvider';
import { AgreementDetailsContextProvider } from 'pages/AgreementDetails/AgreementDetailsContextProvider';
import AvailableAgreementsFiltersModalContent from './AvailableAgreementsFiltersModalContent';

jest.mock('@nv2/nv2-pkg-js-theme/src/components/getBrandColors', () => {
  const getBrandColors = () => ({
    500: '#00000000000',
  });
  return getBrandColors;
});

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    budgetId: '1',
    agreementId: '1',
  }),
}));

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

describe('AgreementDetails: AgreementValues: AgreementsComparison: AgreementsComparisonModal: AvailableAgreementsFiltersModalContent', () => {
  const mockCloseModal = jest.fn();
  const mockSetModalFilters = jest.fn();
  const mockOnApplyFilters = jest.fn();
  const mockOnResetFilters = jest.fn();

  const mockModalFilters = {
    home_operators: [1, 2],
    partner_operators: [3, 4],
    calculation_statuses: ['APPLIED'],
    status: 'ACTIVE',
  };

  const store = configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware({
      serializableCheck: false,
    }),
    preloadedState: {
      agreementsForActionsFilters: {
        isLoading: false,
        data: {
          start_date_min: '2023-01-01',
          start_date_max: '2023-12-31',
          end_date_min: '2023-01-01',
          end_date_max: '2023-12-31',
          statuses: [],
          negotiators: [],
        },
      },
      agreementsForModalAction: {
        isLoading: false,
        data: { results: [] },
      },
    },
  });

  const mockProps = {
    closeModal: mockCloseModal,
    modalFilters: mockModalFilters,
    setModalFilters: mockSetModalFilters,
    onApplyFilters: mockOnApplyFilters,
    onResetFilters: mockOnResetFilters,
  };

  const renderComponent = (props = {}) => {
    const finalProps = { ...mockProps, ...props };

    return render(
      <MemoryRouter initialEntries={['/budgets/1/agreements/1']}>
        <Provider store={store}>
          <AppContextProvider>
            <AgreementDetailsContextProvider>
              <ThemeProvider theme={theme(currentTheme)}>
                <AvailableAgreementsFiltersModalContent {...finalProps} />
              </ThemeProvider>
            </AgreementDetailsContextProvider>
          </AppContextProvider>
        </Provider>
      </MemoryRouter>,
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render filters section', () => {
    renderComponent();

    expect(screen.getByText(/Start Date/i)).toBeInTheDocument();
    expect(screen.getByText(/End Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Status/i)).toBeInTheDocument();
    expect(screen.getByText(/Negotiator/i)).toBeInTheDocument();
  });

  test('should render actions section', () => {
    renderComponent();

    expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reset filtration/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  test('should call onApplyFilters when confirm button is clicked', async () => {
    renderComponent();

    const applyButton = screen.getByRole('button', { name: /confirm/i });
    await act(() => fireEvent.click(applyButton));

    expect(mockOnApplyFilters).toHaveBeenCalled();
  });

  test('should call onResetFilters when reset button is clicked', async () => {
    renderComponent({
      modalFilters: {
        home_operators: [1, 2],
        partner_operators: [3, 4],
        calculation_statuses: ['APPLIED'],
      },
    });

    const resetButton = screen.getByRole('button', { name: /reset filtration/i });
    await act(() => fireEvent.click(resetButton));

    expect(mockOnResetFilters).toHaveBeenCalled();
  });

  test('should call closeModal when cancel button is clicked', async () => {
    renderComponent();

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await act(() => fireEvent.click(cancelButton));

    expect(mockCloseModal).toHaveBeenCalled();
  });

  test('should render with PerfectScrollbar', () => {
    renderComponent();

    const scrollableContainer = document.querySelector('.ps');
    expect(scrollableContainer).toBeInTheDocument();
  });

  test('should pass modalFilters to Filters component', () => {
    renderComponent();

    expect(screen.getByText(/Start Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Status/i)).toBeInTheDocument();
  });

  test('should pass setModalFilters to Filters component', () => {
    renderComponent();

    expect(screen.getByText(/End Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Negotiator/i)).toBeInTheDocument();
  });

  test('should handle missing closeModal prop', () => {
    renderComponent({ closeModal: undefined });

    expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
  });

  test('should render with correct CSS class', () => {
    renderComponent();

    const container = document.querySelector('.agreements-compare-table-filters-modal-content');
    expect(container).toBeInTheDocument();
  });

  test('should handle empty modalFilters', () => {
    renderComponent({ modalFilters: {} });

    expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reset filtration/i })).toBeInTheDocument();
  });

  test('should handle null modalFilters', () => {
    // modalFilters should never be null, pass empty object instead
    renderComponent({ modalFilters: {} });

    expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reset filtration/i })).toBeInTheDocument();
  });

  test('should suppress horizontal scrolling in PerfectScrollbar', () => {
    renderComponent();

    const scrollableContainer = document.querySelector('.ps');
    expect(scrollableContainer).toBeInTheDocument();
  });

  test('should render Filters component with correct props', () => {
    renderComponent();

    expect(screen.getByText(/Start Date/i)).toBeInTheDocument();
    expect(screen.getByText(/End Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Status/i)).toBeInTheDocument();
    expect(screen.getByText(/Negotiator/i)).toBeInTheDocument();
  });

  test('should maintain component structure', () => {
    renderComponent();

    const container = document.querySelector('.agreements-compare-table-filters-modal-content');
    expect(container).toBeInTheDocument();

    const scrollableContainer = container?.querySelector('.ps');
    expect(scrollableContainer).toBeInTheDocument();

    const actionsSection = container?.querySelector('.agreements-compare-table-filters-modal-content__actions');
    expect(actionsSection).toBeInTheDocument();
  });

  test('should handle all required props', () => {
    const requiredProps = {
      modalFilters: { test: 'value' },
      setModalFilters: jest.fn(),
      onApplyFilters: jest.fn(),
      onResetFilters: jest.fn(),
    };

    renderComponent(requiredProps);

    expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /reset filtration/i })).toBeInTheDocument();
  });
});
