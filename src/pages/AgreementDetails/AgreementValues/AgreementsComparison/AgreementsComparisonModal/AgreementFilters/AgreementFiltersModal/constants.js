export const agreementFiltersModal = 'agreement-filters-modal';
export const agreementFiltersModalContent = `${agreementFiltersModal}__content`;
export const agreementFiltersModalReset = `${agreementFiltersModal}__reset`;

export const defaultAgreementGlobalFilters = {
  start_date: '',
  end_date: '',
  home_operators: [],
  partner_operators: [],
  statuses: [],
  negotiators: [],
  calculation_statuses: ['APPLIED'],
};

export const agreementsCompareTableFiltersModalContentModifier = 'agreements-compare-table-filters-modal-content';

export const agreementsCompareTableFiltersModalContentRowModifier = `${agreementsCompareTableFiltersModalContentModifier}__row`;

export const agreementsCompareTableFiltersModalContentBlockModifier = `${agreementsCompareTableFiltersModalContentModifier}__block`;

export const agreementsCompareTableFiltersModalContentActionsModifier = `${agreementsCompareTableFiltersModalContentModifier}__actions`;

export const agreementCompareTableFiltersBtnOpenModalModifier = 'agreements-compare-table-filters__btn';

export const agreementsCompareTableFiltersModalModifier = 'agreements-compare-table-filters__modal';
