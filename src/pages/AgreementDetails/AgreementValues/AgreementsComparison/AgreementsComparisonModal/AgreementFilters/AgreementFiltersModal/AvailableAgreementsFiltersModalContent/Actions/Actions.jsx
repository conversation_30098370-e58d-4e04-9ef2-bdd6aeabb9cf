import React from 'react';
import PropTypes from 'prop-types';
import { FilterActionButtons } from 'shared/TableFilters';
import { useSelector } from 'react-redux';
import useAppliedFiltersCount from 'pages/AgreementDetails/AgreementParameters/AgreementFilters/hooks/useAppliedFiltersCount';
import { agreementsCompareTableFiltersModalContentActionsModifier } from '../../constants';

const Actions = ({
  modalFilters, onApplyFilters, onResetFilters, closeModal,
}) => {
  const isFiltersLoading = useSelector((state) => state.agreementsForActionsFilters.isLoading);
  const isAgreementsLoading = useSelector((state) => state.agreementsForModalAction.isLoading);
  const appliedFiltersCount = useAppliedFiltersCount(modalFilters);

  return (
    <FilterActionButtons
      confirmAction={onApplyFilters}
      resetAction={onResetFilters}
      closeAction={closeModal}
      disabled={!appliedFiltersCount}
      containerClassName={agreementsCompareTableFiltersModalContentActionsModifier}
      isResetButtonDisabled={isFiltersLoading || isAgreementsLoading}
    />
  );
};

Actions.propTypes = {
  modalFilters: PropTypes.instanceOf(Object).isRequired,
  onApplyFilters: PropTypes.func.isRequired,
  onResetFilters: PropTypes.func.isRequired,
  closeModal: PropTypes.func.isRequired,
};

export default Actions;
