import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import dayjs from 'dayjs';
import {
  TextField,
} from '@mui/material';

import { directionConfig } from 'core/contstants';
import FormattedNumber from 'shared/FormattedNumber';
import { dateFormat } from 'shared/RangePicker/constants';
import RangePicker from 'shared/RangePicker';
import Autocomplete from 'shared/Autocomplete';
import RadioButtons from 'shared/RadioButtons';
import {
  callDestinationConfig,
  taxTypeConfig,
  volumeTypeConfig,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConfigs';
import CurrenciesAutocomplete from 'features/CurrenciesAutocomplete';
import OperatorsLabel from 'shared/OperatorsLabel';
import {
  optionKey,
  errorModifier,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/constants';
import getOperatorsFilterOptions from 'core/utilities/getOperatorsFilterOptions';
import DiscountSectionTitle from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/shared/DiscountSectionTitle';
import './DiscountTraffic.scss';
import CalledCountriesSelect from './CalledCountriesSelect';
import {
  discountTrafficAutocompleteModifier,
  discountTrafficCalendarModifier,
  discountTrafficModifier,
  discountTrafficRadioButtonsModifier,
  discountFields,
  discountTrafficInputModifier,
  discountTrafficFormModifier,
}
  from './constants';
import TrafficSegmentsAutocomplete from './TrafficSegmentsAutocomplete';

const DiscountTraffic = ({
  formik,
  additionalFormFieldsConfiguration,
  discountFormConfig,
}) => {
  const homeOperators = useSelector(
    (state) => state.agreementParameters.data?.home_operators) || [];
  const partnerOperators = useSelector(
    (state) => state.agreementParameters.data?.partner_operators) || [];
  const validFrom = useSelector(
    (state) => state.agreementParameters.data?.start_date);
  const validTo = useSelector(
    (state) => state.agreementParameters.data?.end_date);

  const disabledDate = (current) => {
    if (current > dayjs(validTo, dateFormat) || current < dayjs(validFrom, dateFormat)) {
      return true;
    }

    return false;
  };

  const getErrorModifier = (field) => (formik.errors[field] ? errorModifier : '');

  const operatorsFilterOptions = getOperatorsFilterOptions();

  const resetTrafficError = (fieldName) => {
    if (formik.errors[fieldName]) {
      formik.setFieldError(fieldName, null);
    }
  };

  const changeDiscountTraffic = ({ field, value }) => {
    resetTrafficError(field);

    formik.setFieldValue(field, value);
  };

  return (
    <div className={discountTrafficModifier}>
      <DiscountSectionTitle title="Discount Traffic" />
      <div className={discountTrafficFormModifier}>
        <Autocomplete
          name={discountFields.homeOperators}
          data-testid={discountFields.homeOperators}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.homeOperators)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={homeOperators}
          getOptionLabel={(option) => (option[optionKey.pmnCode] ? option[optionKey.pmnCode] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.pmnCode] === value[optionKey.pmnCode]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Home Operators*" />}
          value={formik.values[discountFields.homeOperators]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.homeOperators,
            value,
          })}
          multiple
          optionKey={optionKey.pmnCode}
          filterOptions={operatorsFilterOptions}
          getLabel={(option) => <OperatorsLabel operator={option} />}
        />
        <Autocomplete
          name={discountFields.partnerOperators}
          data-testid={discountFields.partnerOperators}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.partnerOperators)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={partnerOperators}
          getOptionLabel={(option) => (option[optionKey.pmnCode] ? option[optionKey.pmnCode] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.pmnCode] === value[optionKey.pmnCode]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Partner Operators*" />}
          value={formik.values[discountFields.partnerOperators]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.partnerOperators,
            value,
          })}
          multiple
          optionKey={optionKey.pmnCode}
          filterOptions={operatorsFilterOptions}
          getLabel={(option) => <OperatorsLabel operator={option} />}
        />
        <RangePicker
          placeholder={['Valid From', 'Valid To*']}
          disabledDate={disabledDate}
          startDate={formik.values[discountFields.validFrom]}
          endDate={formik.values[discountFields.validTo]}
          onCalendarChange={(startDate, endDate) => {
            changeDiscountTraffic({
              field: discountFields.validFrom,
              value: startDate,
            });
            changeDiscountTraffic({
              field: discountFields.validTo,
              value: endDate,
            });
          }}
          className={`${discountTrafficCalendarModifier} ${getErrorModifier(discountFields.validFrom)} ${getErrorModifier(discountFields.validTo)}`}
        />
        <Autocomplete
          name={discountFields.discountDirection}
          data-testid={discountFields.discountDirection}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.discountDirection)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={directionConfig}
          getOptionLabel={(option) => (option[optionKey.title] ? option[optionKey.title] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.title] === value[optionKey.title]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Discount Direction*" />}
          value={formik.values[discountFields.discountDirection]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.discountDirection,
            value,
          })}
          optionKey={optionKey.title}
          disableClearable
        />
        <Autocomplete
          name={discountFields.serviceTypes}
          data-testid={discountFields.serviceTypes}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.serviceTypes)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={discountFormConfig.availableValues[discountFields.serviceTypes]
            .getAvailableConfig(formik.values[discountFields.serviceTypes])}
          getOptionLabel={(option) => (option[optionKey.title] ? option[optionKey.title] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.title] === value[optionKey.title]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Service Types*" />}
          value={formik.values[discountFields.serviceTypes]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.serviceTypes,
            value,
          })}
          multiple
          optionKey={optionKey.title}
          enableSelectAll={discountFormConfig.availableValues[discountFields.serviceTypes]
            .enableSelectAll}
        />
        {additionalFormFieldsConfiguration[discountFields.callDestinations] && (
        <Autocomplete
          name={discountFields.callDestinations}
          data-testid={discountFields.callDestinations}
          className={discountTrafficAutocompleteModifier}
          autoComplete={false}
          selectOnFocus={false}
          options={callDestinationConfig}
          getOptionLabel={(option) => (option[optionKey.title] ? option[optionKey.title] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.title] === value[optionKey.title]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Call Destinations" />}
          value={formik.values[discountFields.callDestinations]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.callDestinations,
            value,
          })}
          multiple
          optionKey={optionKey.title}
          disabled={!!formik.values[discountFields.calledCountries].length}
        />
        )}
        {additionalFormFieldsConfiguration[discountFields.calledCountries] && (
        <CalledCountriesSelect
          changeDiscountTraffic={changeDiscountTraffic}
          formikValues={formik.values}
        />
        )}
        {!!discountFormConfig.availableValues[discountFields.imsiCountType] && (
        <Autocomplete
          name={discountFields.imsiCountType}
          data-testid={discountFields.imsiCountType}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.imsiCountType)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={discountFormConfig
            .availableValues[discountFields.imsiCountType]}
          getOptionLabel={(option) => (option[optionKey.title] ? option[optionKey.title] : '')}
            /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.title] === value[optionKey.title]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="IMSI Count Type" />}
          value={formik.values[discountFields.imsiCountType]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.imsiCountType,
            value,
          })}
          optionKey={optionKey.title}
          disableClearable
        />
        )}
        {additionalFormFieldsConfiguration[discountFields.trafficSegments] && (
        <TrafficSegmentsAutocomplete
          changeDiscountTraffic={changeDiscountTraffic}
          trafficSegmentsValue={formik.values[discountFields.trafficSegments]}
          homeOperators={formik.values[discountFields.homeOperators]}
        />
        )}
        <Autocomplete
          name={discountFields.discountSettlementMethod}
          data-testid={discountFields.discountSettlementMethod}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.discountSettlementMethod)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={discountFormConfig
            .availableValues[discountFields.discountSettlementMethod]}
          getOptionLabel={(option) => (option[optionKey.title] ? option[optionKey.title] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.title] === value[optionKey.title]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label="Discount Settlement Method*" />}
          value={formik.values[discountFields.discountSettlementMethod]}
          onChange={(e, value) => changeDiscountTraffic({
            field: discountFields.discountSettlementMethod,
            value,
          })}
          optionKey={optionKey.title}
          disableClearable
        />
        <CurrenciesAutocomplete
          name={discountFields.discountCurrency}
          data-testid={discountFields.discountCurrency}
          onCurrencyChange={(e, value) => changeDiscountTraffic({
            field: discountFields.discountCurrency,
            value,
          })}
          currency={formik.values[discountFields.discountCurrency]}
          className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.discountCurrency)}`}
          label="Discount Currency*"
          disableClearable
        />
        {additionalFormFieldsConfiguration[discountFields.rateAboveCommitment] && (
        <FormattedNumber
          isInput
          value={
             formik.values[discountFields.rateAboveCommitment]
          }
          onValueChange={(inputValues) => {
            changeDiscountTraffic({
              field: discountFields.rateAboveCommitment,
              value: inputValues.value,
            });
          }}
          valueIsNumericString
          className={`${discountTrafficInputModifier} ${getErrorModifier(discountFields.rateAboveCommitment)}`}
          label="Rate above commitment"
        />
        )}
        {additionalFormFieldsConfiguration[discountFields.inboundMarketShare] && (
        <FormattedNumber
          isInput
          value={
                formik.values[discountFields.inboundMarketShare]
              }
          onValueChange={(inputValues) => {
            changeDiscountTraffic({
              field: discountFields.inboundMarketShare,
              value: inputValues.value,
            });
          }}
          valueIsNumericString
          className={`${discountTrafficInputModifier} ${getErrorModifier(discountFields.inboundMarketShare)}`}
          label="Inbound Market Share"
        />
        )}
        <RadioButtons
          value={formik.values[discountFields.taxType]}
          config={taxTypeConfig}
          title="Tax Type"
          className={discountTrafficRadioButtonsModifier}
          onChange={(e) => changeDiscountTraffic({
            field: discountFields.taxType,
            value: e.target.value,
          })}
        />
        <RadioButtons
          value={formik.values[discountFields.volumeType]}
          config={volumeTypeConfig}
          title="Volume Type"
          className={discountTrafficRadioButtonsModifier}
          onChange={(e) => changeDiscountTraffic({
            field: discountFields.volumeType,
            value: e.target.value,
          })}
        />
      </div>
    </div>
  );
};

DiscountTraffic.propTypes = {
  formik: PropTypes.shape({
    values: PropTypes.instanceOf(Object),
    setFieldValue: PropTypes.func,
    setFieldError: PropTypes.func,
    errors: PropTypes.instanceOf(Object),
  }),
  additionalFormFieldsConfiguration: PropTypes.shape({
    [discountFields.callDestinations]: PropTypes.bool,
    [discountFields.calledCountries]: PropTypes.bool,
    [discountFields.trafficSegments]: PropTypes.bool,
    [discountFields.rateAboveCommitment]: PropTypes.bool,
    [discountFields.inboundMarketShare]: PropTypes.bool,
  }),
  discountFormConfig: PropTypes.shape({
    availableValues: PropTypes.shape({
      [discountFields.discountSettlementMethod]: PropTypes.instanceOf(Array).isRequired,
      [discountFields.serviceTypes]: PropTypes.instanceOf(Object).isRequired,
    }),
  }).isRequired,
};

DiscountTraffic.defaultProps = {
  formik: {
    values: {
      parameters: [],
    },
    setFieldError: () => {},
    setFieldValue: () => {},
    errors: {},
  },
  additionalFormFieldsConfiguration: {
    [discountFields.callDestinations]: false,
    [discountFields.calledCountries]: false,
    [discountFields.trafficSegments]: false,
    [discountFields.rateAboveCommitment]: false,
    [discountFields.inboundMarketShare]: false,
  },
};

export default DiscountTraffic;
