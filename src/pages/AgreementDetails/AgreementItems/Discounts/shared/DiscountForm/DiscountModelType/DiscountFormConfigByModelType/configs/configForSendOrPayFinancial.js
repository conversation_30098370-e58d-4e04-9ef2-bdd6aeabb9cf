import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';
import {
  commitmentDistributionParameters, commitmentDistributionParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/constants';

import {
  getAllServiceTypesCombinations,
} from './utilities';

const configForSendOrPayFinancial = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAllServiceTypesCombinations,
      enableSelectAll: true,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.sendOrPayFinancial,
        ],
        [discountParametersFields.discountBasis]: [],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: true,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.financial,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
    ],
    [commitmentDistributionParameters]: [
      {
        [commitmentDistributionParametersFields.homeOperators]: [],
        [commitmentDistributionParametersFields.partnerOperators]: [],
        [commitmentDistributionParametersFields.charge]: {
          isDisabled: false,
        },
      },
      {
        [commitmentDistributionParametersFields.homeOperators]: [],
        [commitmentDistributionParametersFields.partnerOperators]: [],
        [commitmentDistributionParametersFields.charge]: {
          isDisabled: false,
        },
      },
    ],
  },
  maxParametersQuantity: 1,
  minParametersQuantity: 1,
  additionalFields: {},
};

export default configForSendOrPayFinancial;
