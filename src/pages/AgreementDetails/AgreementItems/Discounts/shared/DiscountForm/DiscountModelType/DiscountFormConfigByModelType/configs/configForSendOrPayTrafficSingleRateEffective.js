import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getDefaultServiceTypesCombinationBasedOnValue,
} from './utilities';

const configForSendOrPayTrafficSingleRateEffective = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getDefaultServiceTypesCombinationBasedOnValue,
      enableSelectAll: false,
    },
    [discountFields.inboundMarketShare]: {
      isDisabled: false,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.sendOrPayTraffic,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
          boundTypeOptions.marketShare,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.singleRateEffective,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: true,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
    ],
  },
  maxParametersQuantity: 2,
  minParametersQuantity: 2,
  additionalFields: {},
};

export default configForSendOrPayTrafficSingleRateEffective;
