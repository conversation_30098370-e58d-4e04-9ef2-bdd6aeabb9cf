import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getDefaultServiceTypesCombinationBasedOnValue,
} from './utilities';

const configForSteppedTiered = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getDefaultServiceTypesCombinationBasedOnValue,
      enableSelectAll: false,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.steppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.steppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
    ],
  },
  maxParametersQuantity: undefined,
  minParametersQuantity: 2,
  additionalFields: {},
};

export default configForSteppedTiered;
