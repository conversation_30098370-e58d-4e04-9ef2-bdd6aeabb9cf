import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getAllServiceTypesCombinations,
} from './utilities';

const configForAllYouCanEat = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAllServiceTypesCombinations,
      enableSelectAll: true,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.allYouCanEat,
        ],
        [discountParametersFields.discountBasis]: [],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: true,
        },
        [discountParametersFields.boundType]: [boundTypeOptions.financial],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
    ],
  },
  maxParametersQuantity: 1,
  minParametersQuantity: 1,
  additionalFields: {},
};

export default configForAllYouCanEat;
