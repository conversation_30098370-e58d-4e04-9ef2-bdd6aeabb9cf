import {
  discountSettlementMethodOptions, imsiContTypeOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getAccessFeeServiceType,
} from './utilities';

const configForPerMonthPerIMSISteppedTiered = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAccessFeeServiceType,
      enableSelectAll: false,
    },
    [discountFields.imsiCountType]: [
      imsiContTypeOptions.data,
    ],
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.perMonthPerIMSISteppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.uniqueIMSICountPerMonth,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.perMonthPerIMSISteppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.uniqueIMSICountPerMonth,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
    ],
  },
  maxParametersQuantity: undefined,
  minParametersQuantity: 2,
  additionalFields: {
    [discountFields.imsiCountType]: true,
  },
};

export default configForPerMonthPerIMSISteppedTiered;
