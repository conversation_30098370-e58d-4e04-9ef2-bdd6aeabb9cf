import {
  discountSettlementMethodOptions, imsiContTypeOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getAccessFeeDataServiceType,
} from './utilities';

const configForPerMonthPerIMSIWithIncrementalCharging = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAccessFeeDataServiceType,
      enableSelectAll: false,
    },
    [discountFields.imsiCountType]: [
      imsiContTypeOptions.data,
    ],
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.perMonthPerIMSIWithIncrementalCharging,
        ],
        [discountParametersFields.discountBasis]: [],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: true,
        },
        [discountParametersFields.boundType]: [
          boundTypeOptions.volumeIncludedInAccessFee,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
        [discountParametersFields.accessFeeRate]: {
          isDisabled: false,
        },
        [discountParametersFields.incrementalRate]: {
          isDisabled: false,
        },
      },
    ],
  },
  maxParametersQuantity: 1,
  minParametersQuantity: 1,
  additionalFields: {
    [discountParametersFields.accessFeeRate]: true,
    [discountFields.imsiCountType]: true,
  },
};

export default configForPerMonthPerIMSIWithIncrementalCharging;
