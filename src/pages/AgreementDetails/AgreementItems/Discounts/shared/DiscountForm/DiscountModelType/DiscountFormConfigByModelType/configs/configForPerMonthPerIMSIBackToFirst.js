import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  boundTypeOptions,
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getAccessFeeServiceType,
} from './utilities';

const configForPerMonthPerIMSIBackToFirst = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAccessFeeServiceType,
      enableSelectAll: false,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.perMonthPerIMSIBackToFirst,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [boundTypeOptions.uniqueIMSICountPerMonth],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
    ],
  },
  maxParametersQuantity: 1,
  minParametersQuantity: 1,
  additionalFields: {},
};

export default configForPerMonthPerIMSIBackToFirst;
