import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getAccessFeeServiceType,
} from './utilities';

const configForPerMonthPerIMSI = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getAccessFeeServiceType,
      enableSelectAll: false,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.perMonthPerIMSI,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.boundType]: [],
        [discountParametersFields.lowerBound]: {
          isDisabled: true,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
    ],
  },
  maxParametersQuantity: 1,
  minParametersQuantity: 1,
  additionalFields: {},
};

export default configForPerMonthPerIMSI;
