import {
  discountSettlementMethodOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/discountsConstants';
import {
  discountFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/constants';
import {
  discountParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/constants';
import calculationTypesOptions
, {
  balancingOptions, boundTypeOptions,
  discountBasisOptions,
} from 'pages/AgreementDetails/AgreementItems/Discounts/parametersConstants';

import {
  getDefaultServiceTypesCombinationBasedOnValue,
} from './utilities';

const configForBalancedUnbalancedSteppedTiered = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [
      discountSettlementMethodOptions.creditNoteEoA,
    ],
    [discountFields.serviceTypes]: {
      getAvailableConfig: getDefaultServiceTypesCombinationBasedOnValue,
      enableSelectAll: false,
    },
    parameters: [
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.singleRateEffective,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.balancing]: [balancingOptions.balanced],
        [discountParametersFields.boundType]: [],
        [discountParametersFields.lowerBound]: {
          isDisabled: true,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: true,
        },
      },
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.steppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.balancing]: [balancingOptions.unBalanced],
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
      {
        [discountParametersFields.calculationType]: [
          calculationTypesOptions.steppedTiered,
        ],
        [discountParametersFields.discountBasis]: [
          discountBasisOptions.value,
        ],
        [discountParametersFields.discountBasisValue]: {
          isDisabled: false,
        },
        [discountParametersFields.balancing]: [balancingOptions.unBalanced],
        [discountParametersFields.boundType]: [
          boundTypeOptions.volume,
        ],
        [discountParametersFields.lowerBound]: {
          isDisabled: false,
        },
        [discountParametersFields.upperBound]: {
          isDisabled: false,
        },
      },
    ],
  },
  maxParametersQuantity: undefined,
  minParametersQuantity: 3,
  additionalFields: {
    [discountParametersFields.balancing]: true,
  },
};

export default configForBalancedUnbalancedSteppedTiered;
