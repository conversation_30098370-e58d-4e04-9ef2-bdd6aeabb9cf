.agreement-items {
  margin-top: 15px;
  padding: 15px;

  &__tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: auto;

    .MuiBox-root {
      border-bottom: none !important;
    }
  }

  &__tab-panel {
    padding-top: 15px;
  }

  &__toggle-button {
    & polyline {
      stroke: $dark-color-300;
    }

    &:hover {
      & polyline {
        stroke: $brand-blue-color-500;
      }
    }

    &[aria-selected="true"] {
      & svg {
        transform: rotate(180deg) !important;
        transition: transform 2ms;
      }
    }
  }

  &-tab {
    &__data {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      background-color: $light-color-100;
      border-radius: 4px;
      font-family: $primary-font-family;
      color: $dark-color-500;
      font-weight: 700;
    }
  }

  .MuiPaper-root {
    max-width: unset !important;
  }
}
