.budget-list-main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  height: 80px;
  width: 100%;
  background-color: $white-color;
  position: sticky;
  top: 0;
  z-index: 2;

  @media (max-width: $small-tablet-width) {
    margin-bottom: 15px;
  }

  @media (max-width: $mobile-width) {
    margin-top: 25px;
  }

  @media (max-width: $small-mobile-width) {
    flex-direction: column;
    height: auto;
    align-items: flex-start;
    gap: 15px;
  }

  &__info, &__buttons {
    display: flex;
    align-items: center;
  }

  &__icon {
    width: 50px;
    height: 50px;
    box-shadow: $shadow8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__title {
    text-transform: uppercase !important;
    font-size: 20px !important;
    font-weight: bold !important;

    @media (max-width: $small-mobile-width) {
      font-size: 14px !important;
    }
  }

  &__subtitle {
    color: $dark-color-300 !important;
    font-size: 14px !important;

    @media (max-width: $small-mobile-width) {
      font-size: 12px !important;
    }
  }

  &__info {
    gap: 10px;
  }
}
