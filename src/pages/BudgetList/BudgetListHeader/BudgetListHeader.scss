.budget-list-header {
  font-weight: 700 !important;
  background-color: $app-background-color !important;
  color: $dark-color-500 !important;
  position: sticky;
  top: 80px;
  z-index: 2;

  &__wrap {
    padding: 10px 10px 10px 20px;

    @media (max-width: $small-desktop-width) {
      grid-template-columns: 1fr auto;
      grid-template-areas: ". kpi";
    }

    @media (max-width: $small-mobile-width) {
      display: none;
    }
  }

  &__home-operators {
    @media (max-width: $small-desktop-width) {
      display: none;
    }
  }

  &__period,
  &__type,
  &__title {
    @media (max-width: $small-desktop-width) {
      display: none;
    }
  }

  &__title {
    font-weight: 700 !important;
  }

  &__icon {
    & path {
      stroke: $dark-color-300;
    }
  }

  &__budget-figures {
    &__item:hover {
      background: var(--brand-blue-color-50);
    }

    @media (max-width: $small-desktop-width) {
      display: none;
    }

    @media (max-width: 1024px) {
      justify-self: end;
    }
  }

  &__kpi-component {
    display: flex;
    margin-left: 30px;

    @media (max-width: $small-desktop-width) {
      margin-left: 0;
      justify-self: end;
    }

    &__title {
      @media (max-width: $small-desktop-width) {
        font-weight: 700;
      }
    }
  }
}

.budget-list-item-template__title-wrap {
  &:first-child {
    .budget-list-header__title {
      margin-left: 45px !important;
    }
  }
}
