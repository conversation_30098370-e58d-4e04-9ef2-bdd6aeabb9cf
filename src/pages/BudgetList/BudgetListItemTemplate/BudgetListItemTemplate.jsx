import React from 'react';
import PropTypes from 'prop-types';
import {
  Checkbox,
  Paper, Skeleton, Tooltip, Typography,
} from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import {
  budgetFiguresTitle, budgetFiguresResponsiveTitle, headerData,
} from 'pages/BudgetList/constants';
import useDeviceResolution from 'core/hooks/useDeviceResolution';
import BudgetListItemInfo from 'pages/BudgetList/BudgetListItemInfo';
import BudgetDescription from 'shared/BudgetDescription';
import Dotdotdot from 'react-dotdotdot';
import './BudgetListItemTemplate.scss';

const BudgetListItemTemplate = ({
  title, homeOperators, period, className, budgetFigures, budgetFiguresIsLoading,
  type, KPIComponent, actionsButtons, id, isBudgetItem,
  checkedForComparison, onClickComparisonCheckbox, createdAt, updatedAt, description,
}) => {
  const { isTablet, isSmallDesktop } = useDeviceResolution();
  const isMediumScreen = isSmallDesktop && !isTablet;
  const descriptionClamp = isMediumScreen ? 2 : 3;

  const budgetFiguresItems = budgetFigures.map((item, key) => (isSmallDesktop ? (
    <div className="budget-list-item-template__budget-figures-wrap">
      <Typography
        key={budgetFiguresResponsiveTitle[key].toString()}
        variant="body2"
        className={`budget-list-item-template__budget-figures__title ${className}__budget-figures__title`}
      >
        {budgetFiguresResponsiveTitle[key]}
      </Typography>
      <div className={`budget-list-item-template__budget-figures__item ${className}__budget-figures__item`}>
        {item.data}
      </div>
    </div>
  ) : (
    <Tooltip
      key={budgetFiguresTitle[key].toString()}
      title={budgetFiguresTitle[key]}
      placement="top"
      arrow
    >
      <div className={`budget-list-item-template__budget-figures__item ${className}__budget-figures__item`}>
        {item.data}
      </div>
    </Tooltip>
  )));

  const budgetFiguresItemsPreloader = (
    <Skeleton
      variant="rect"
      className="budget-list-item-template__budget-figures-preloader"
      animation="wave"
      data-testid="budget-figures-preloader"
    />
  );

  return (
    <Paper className={className}>
      <div
        className={`budget-list-item-template ${className}__wrap`}
        data-testid="budget-list-item-template"
        id={id}
      >
        <div className="budget-list-item-template__title-wrap">
          {isBudgetItem && (
          <Checkbox
            icon={<CheckBoxOutlineBlankIcon fontSize="small" />}
            checkedIcon={<CheckBoxIcon fontSize="small" />}
            style={{ marginRight: 8 }}
            checked={checkedForComparison}
            onClick={(e) => {
              e.preventDefault();

              onClickComparisonCheckbox({
                budgetId: id,
                state: e.target.checked,
                budgetName: title,
              });
            }}
          />
          )}
          <div className={`budget-list-item-template__title-content ${className}__title`}>
            <Typography className={`budget-list-item-template__title ${className}__title`} component="div">
              <Dotdotdot clamp={2}>
                {title}
              </Dotdotdot>
            </Typography>
            {description && (
              <BudgetDescription description={description} clamp={descriptionClamp} />
            )}
          </div>
          {isBudgetItem && (
            <BudgetListItemInfo
              createdAt={createdAt}
              updatedAt={updatedAt}
              description={description}
            />
          )}
        </div>
        <div className={`budget-list-item-template__type ${className}__type`}>
          {isSmallDesktop && (
          <Typography
            variant="body2"
            className="budget-list-item-template__type-title"
          >
            {headerData.type}
          </Typography>
          )}
          {type}
        </div>
        <div className={`budget-list-item-template__home-operators ${className}__home-operators`}>
          {isSmallDesktop && (
          <Typography
            variant="body2"
            className="budget-list-item-template__home-operators-title"
          >
            {headerData.homeOperators}
          </Typography>
          )}
          {homeOperators}
        </div>
        <div className="budget-list-item-template__divider" />
        <div className={`budget-list-item-template__period ${className}__period`}>
          {isSmallDesktop && (
          <Typography
            variant="body2"
            className="budget-list-item-template__period-title"
          >
            {headerData.period}
          </Typography>
          )}
          {period}
        </div>
        <div className={`budget-list-item-template__budget-figures ${className}__budget-figures`}>
          {budgetFiguresIsLoading
            ? budgetFiguresItemsPreloader
            : budgetFiguresItems}
        </div>
        <div className={`budget-list-item-template__kpi-component ${className}__kpi-component`}>
          {KPIComponent}
        </div>
        {actionsButtons && (
        <div
          className={`budget-list-item-template__action-btn ${className}__action-btn`}
          data-testid="budget-list-item-template__action-btn"
        >
          {actionsButtons}
        </div>
        )}
      </div>
    </Paper>
  );
};

BudgetListItemTemplate.propTypes = {
  title: PropTypes.string.isRequired,
  homeOperators: PropTypes.oneOfType([
    PropTypes.instanceOf(Object),
    PropTypes.string,
  ]).isRequired,
  period: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Object)]).isRequired,
  className: PropTypes.string.isRequired,
  type: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
  budgetFigures: PropTypes.instanceOf(Array).isRequired,
  budgetFiguresIsLoading: PropTypes.bool,
  KPIComponent: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.string,
  ]),
  actionsButtons: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.string,
  ]),
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  isBudgetItem: PropTypes.bool,
  checkedForComparison: PropTypes.bool,
  onClickComparisonCheckbox: PropTypes.func,
  createdAt: PropTypes.string,
  updatedAt: PropTypes.string,
  description: PropTypes.string,
};
BudgetListItemTemplate.defaultProps = {
  budgetFiguresIsLoading: false,
  KPIComponent: '',
  actionsButtons: '',
  id: '',
  isBudgetItem: false,
  checkedForComparison: false,
  onClickComparisonCheckbox: () => {},
  createdAt: '',
  updatedAt: '',
  description: '',
};

export default BudgetListItemTemplate;
