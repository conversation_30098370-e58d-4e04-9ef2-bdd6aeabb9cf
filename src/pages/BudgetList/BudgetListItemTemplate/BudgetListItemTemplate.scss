.budget-list-item-template {
  width: 100%;
  align-items: center;
  gap: 0 15px;
  display: grid;
  grid-template-columns: minmax(270px, 2fr) 1fr 1fr 2fr 1fr 3.3fr 0.7fr;
  grid-template-areas: "title type home-operators period figures kpi action";

  &__title {
    &-wrap {
      margin-right: 10px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      grid-area: title;

      @media (max-width: $small-desktop-width) {
        margin-left: -10px !important;
      }

      &__title-content {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &__home-operators {
    max-width: 135px;
    white-space: wrap;
    grid-area: home-operators;

    &-title {
      color: $dark-color-300 !important;
      margin-bottom: 8px !important;
    }

    @media (max-width: $small-desktop-width) {
      max-width: 100%;
    }
  }

  &__divider {
    grid-area: divider;
    border-bottom: 1px solid $border-color;
  }

  &__period {
    white-space: nowrap;
    grid-area: period;

    &-title {
      color: $dark-color-300 !important;
      margin-bottom: 8px !important;
    }

    @media (max-width: $small-desktop-width) {
      min-width: 180px;
    }
  }

  &__type {
    grid-area: type;

    &-title {
      color: $dark-color-300 !important;
      margin-bottom: 8px !important;
    }
  }

  &__kpi-component {
    grid-area: kpi;
  }

  &__budget-figures {
    display: flex;
    justify-content: center;
    gap: 25px;
    grid-area: figures;

    @media (max-width: $small-desktop-width) {
      justify-content: flex-start;
      gap: 35px;
    }

    &__item {
      width: 100px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-preloader {
      height: 40px !important;
      width: 260px;
      border-radius: 2px;
      background-color: $light-color-100 !important;
    }

    &__title {
      color: $dark-color-300 !important;
      margin-bottom: 8px !important;
    }
  }

  &__action-btn {
    grid-area: action;
  }

  .budget-creation {
    margin-left: unset !important;
  }
}
