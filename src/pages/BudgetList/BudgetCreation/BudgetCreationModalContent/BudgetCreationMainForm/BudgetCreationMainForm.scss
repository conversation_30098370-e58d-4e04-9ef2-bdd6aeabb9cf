.budget-creation {
  &-modal {
    &__range-picker-label {
      width: 100%;
      display: block !important;
      margin-bottom: 9px !important;
      color: $dark-color-300;
      font-weight: bold !important;
    }

    &__period-wrap {
      display: flex;
      gap: 14px;

      &__period {
        display: flex;
        align-items: center;
        margin-right: 14px !important;

        @media (max-width: $mobile-width) {
          flex-direction: column;
          align-items: flex-start;
        }
      }

      @media (max-width: $tablet-width) {
        display: flex;
        margin-top: 25px;
      }

      @media (max-width: $mobile-width) {
        flex-direction: column;
      }
    }

    &__name-wrap {
      width: 350px;
      align-items: normal;
      margin-right: 55px !important;

      .MuiFormLabel-root {
        transform: translate(12px, 12px) scale(1);
        background-color: $white-color;
        padding: 0 5px;
      }

      @media (max-width: $tablet-width) {
        width: 100%;
        margin-right: 0 !important;
      }
    }

    &__description-wrap {
      width: 350px;
      align-items: normal;
      margin-right: 55px !important;

      .MuiFormLabel-root {
        transform: translate(12px, 12px) scale(1);
        background-color: $white-color;
        padding: 0 5px;
      }

      .MuiOutlinedInput-input {
        width: 100%;
      }

      @media (max-width: $tablet-width) {
        width: 100%;
        margin-right: 0 !important;
        margin-top: 25px !important;
      }
    }

    &__lhm-wrap {
      display: flex;
      align-items: center;
    }

    &__operators-wrap {
      .home-operators-autocomplete {
        width: 350px;
        align-items: normal;
        margin-right: 55px !important;
        margin-bottom: 25px;

        @media (max-width: $tablet-width) {
          margin-right: 0 !important;
          width: 100%;
        }
      }

      @media (max-width: $tablet-width) {
        width: 100%;
      }
    }

    .budget-creation-modal__main-form {
      padding-bottom: 25px;
      border-bottom: 1px dashed $light-color-500;
    }
  }
}
