import {
  getIOTRatesFiltersRequest,
  getIOTRatesFiltersSuccess,
  getIOTRatesFiltersFailure,
  getIOTRatesFiltersCanceled,
} from './actionsCreators';
import getIOTRatesFilters from './api.service';

const getIOTRatesFiltersAction = (controller, params) => async (dispatch) => {
  try {
    const { signal } = controller;

    dispatch(getIOTRatesFiltersRequest());

    const { data } = await getIOTRatesFilters(signal, params);

    dispatch(getIOTRatesFiltersSuccess(data));

    return data;
  } catch (error) {
    if (controller.signal?.aborted) {
      dispatch(getIOTRatesFiltersCanceled(error));
    } else {
      dispatch(getIOTRatesFiltersFailure(error));
    }

    throw error;
  }
};

export default getIOTRatesFiltersAction;
