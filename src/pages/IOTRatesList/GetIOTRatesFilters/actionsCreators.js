import {
  GET_IOT_RATES_FILTERS_REQUEST,
  GET_IOT_RATES_FILTERS_SUCCESS,
  GET_IOT_RATES_FILTERS_FAILURE,
  GET_IOT_RATES_FILTERS_CANCELED,
} from './actionTypes';

export const getIOTRatesFiltersRequest = () => (
  { type: GET_IOT_RATES_FILTERS_REQUEST });
export const getIOTRatesFiltersSuccess = (data) => (
  { type: GET_IOT_RATES_FILTERS_SUCCESS, data });
export const getIOTRatesFiltersFailure = (error) => (
  { type: GET_IOT_RATES_FILTERS_FAILURE, error });
export const getIOTRatesFiltersCanceled = (error) => (
  { type: GET_IOT_RATES_FILTERS_CANCELED, error });
