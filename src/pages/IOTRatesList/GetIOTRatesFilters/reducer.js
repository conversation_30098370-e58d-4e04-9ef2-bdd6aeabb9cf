import {
  GET_IOT_RATES_FILTERS_REQUEST,
  GET_IOT_RATES_FILTERS_SUCCESS,
  GET_IOT_RATES_FILTERS_FAILURE,
  GET_IOT_RATES_FILTERS_CANCELED,
} from './actionTypes';

const initialState = {
  data: {},
  isLoading: true,
};

const getIOTRatesFiltersReducer = (state = initialState, {
  type, data, error,
}) => {
  switch (type) {
    case GET_IOT_RATES_FILTERS_REQUEST:
      return {
        ...state,
        isLoading: true,
      };
    case GET_IOT_RATES_FILTERS_SUCCESS:
      return {
        ...state,
        data,
        isLoading: false,
      };
    case GET_IOT_RATES_FILTERS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error,
      };
    case GET_IOT_RATES_FILTERS_CANCELED:
      return {
        ...state,
        error,
      };
    default:
      return state;
  }
};

export default getIOTRatesFiltersReducer;
