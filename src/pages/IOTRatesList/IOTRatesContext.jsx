import React, {
  createContext, useContext, useMemo, useState,
} from 'react';
import PropTypes from 'prop-types';
import { defaultIOTRatesFilters } from 'pages/IOTRatesList/IOTRatesFilters/constants';

export const IOTRatesContext = createContext({});

export const IOTRatesProvider = ({
  children,
  dispatchGetIOTRatesFiltersAction,
}) => {
  const [iotRatesFilters, setIotRatesFilters] = useState(defaultIOTRatesFilters);

  const value = useMemo(() => ({
    iotRatesFilters,
    setIotRatesFilters,
    dispatchGetIOTRatesFiltersAction,
  }), [
    iotRatesFilters,
    dispatchGetIOTRatesFiltersAction,
  ]);

  return (
    <IOTRatesContext.Provider value={value}>
      {children}
    </IOTRatesContext.Provider>
  );
};

IOTRatesProvider.propTypes = {
  children: PropTypes.node.isRequired,
  dispatchGetIOTRatesFiltersAction: PropTypes.func.isRequired,
};

export const useIOTRatesContext = () => useContext(IOTRatesContext);

export default IOTRatesProvider;
