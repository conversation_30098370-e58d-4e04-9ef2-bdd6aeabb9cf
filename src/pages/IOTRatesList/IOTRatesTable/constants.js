export const iotRatesTableClass = 'iot-rates-table';
export const maxTableHeight = 'calc(100vh - 305px)';
export const rowHeight = 52;
export const tooltipWidth = '600px';
export const ALL = 'all';

export const iotRatesFields = {
  id: 'id',
  homeOperator: 'home_operator',
  partnerOperator: 'partner_operator',
  direction: 'traffic_direction',
  serviceType: 'service_type',
  calledCountries: 'called_countries',
  isPremium: 'is_premium',
  startDate: 'start_date',
  endDate: 'end_date',
  type: 'type',
  currency: 'currency_code',
  rate: 'value',
};

export const defaultPageSizeOptions = [10, 50, 100];

export const directionOptions = [
  { value: 'INBOUND', title: 'Inbound' },
  { value: 'OUTBOUND', title: 'Outbound' },
];

export const isPremiumOptions = [
  { value: ALL, title: 'All' },
  { value: true, title: 'Yes' },
  { value: false, title: 'No' },
  { value: 'null', title: '-' },
];

export const typeOptions = [
  { value: 'STANDARD', title: 'Standard' },
  { value: 'EU_REGULATED', title: 'EU Regulated' },
  { value: 'DISCOUNTED', title: 'Discounted' },
];
