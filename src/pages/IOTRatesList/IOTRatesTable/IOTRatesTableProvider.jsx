import React from 'react';
import { useDispatch } from 'react-redux';
import {
  MuiTableContext,
} from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';

import { HTTPService } from 'core/services';
import getIOTRatesAction from 'pages/IOTRatesList/GetIOTRates/actions';
import { useIOTRatesContext } from 'pages/IOTRatesList/IOTRatesContext';
import getFormattedSortFieldForRequest from 'core/utilities/getFormattedSortFieldForRequest';
import getIds from 'core/utilities/getIds';
import IOTRatesTable from './IOTRatesTable';
import { ALL } from './constants';

const { MuiTableProvider } = MuiTableContext;
let iotRatesController = new AbortController();

export const IOTRatesTableProvider = () => {
  const { iotRatesFilters } = useIOTRatesContext();
  const dispatch = useDispatch();

  const dispatchGetIOTRatesAction = (tableParams) => {
    HTTPService.cancelRequest(iotRatesController);
    iotRatesController = HTTPService.getController();

    const {
      home_operators: homeOperators,
      partner_operators: partnerOperators,
      is_premium: isPremium,
      ...restFilters
    } = iotRatesFilters;

    const filters = {
      ...restFilters,
      home_operators: getIds(homeOperators),
      partner_operators: getIds(partnerOperators),
      ...(isPremium !== ALL && { is_premium: isPremium }),
    };

    const params = {
      ...tableParams,
      ...filters,
    };

    dispatch(getIOTRatesAction(iotRatesController, params));
  };

  const onChangeIOTRates = ({ page, pageSize }, sort, search) => {
    const params = {
      page,
      page_size: pageSize,
      search,
      sort_field: getFormattedSortFieldForRequest(sort),
    };

    dispatchGetIOTRatesAction(params);
  };

  return (
    <MuiTableProvider
      onChange={onChangeIOTRates}
    >
      <IOTRatesTable
        dispatchGetIOTRatesAction={dispatchGetIOTRatesAction}
        iotRatesFilters={iotRatesFilters}
      />
    </MuiTableProvider>
  );
};

export default IOTRatesTableProvider;
