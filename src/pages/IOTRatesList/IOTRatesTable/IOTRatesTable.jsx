import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import MuiTable, { MuiTableContext, MuiTableConstants } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { useAppContext } from 'AppContextProvider';
import IOTRatesFilters from 'pages/IOTRatesList/IOTRatesFilters/IOTRatesFilters';
import {
  iotRatesTableClass,
  maxTableHeight,
  rowHeight,
  defaultPageSizeOptions,
} from 'pages/IOTRatesList/IOTRatesTable/constants';

import {
  iotRatesTableConfig,
  noDataConfig,
} from 'pages/IOTRatesList/IOTRatesTable/configs';
import getFormattedSortFieldForRequest from 'core/utilities/getFormattedSortFieldForRequest';

import './IOTRatesTable.scss';

const { useMuiTableContext } = MuiTableContext;
const { firstPage } = MuiTableConstants;

const IOTRatesTable = ({
  dispatchGetIOTRatesAction,
  iotRatesFilters,
}) => {
  const {
    pagination,
    setPagination,
    searchValue,
    sort,
  } = useMuiTableContext();

  const iotRatesCount = useSelector((state) => state.iotRates.data.count || 0);
  const iotRates = useSelector((state) => state.iotRates.data.results) || [];
  const isIOTRatesLoading = useSelector((state) => state.iotRates.isLoading);

  const { primaryColor, getBrandColors } = useAppContext();

  const getIOTRates = () => {
    if (pagination.page !== firstPage) {
      setPagination({ page: firstPage, pageSize: pagination.pageSize });
    }

    const params = {
      page: firstPage,
      page_size: pagination.pageSize,
      search: searchValue,
      sort_field: getFormattedSortFieldForRequest(sort[0]),
    };

    dispatchGetIOTRatesAction(params);
  };

  useEffect(() => {
    getIOTRates();
  }, [iotRatesFilters]);

  const getIOTRatesTableActions = () => (
    <div className={`${iotRatesTableClass}__actions`}>
      <IOTRatesFilters
        isReadOnly={false}
      />
    </div>
  );

  return (
    <div
      data-testid={`${iotRatesTableClass}__wrap`}
      className={`${iotRatesTableClass}__wrap`}
    >
      <MuiTable
        rows={iotRates}
        columns={iotRatesTableConfig}
        loading={isIOTRatesLoading}
        primaryColor={primaryColor}
        getCurrentThemeColors={getBrandColors}
        isVisibleSearchInput
        disableVirtualization
        noDataConfig={noDataConfig(primaryColor)}
        rowCount={iotRatesCount}
        pageSizeOptions={defaultPageSizeOptions}
        maxTableHeight={maxTableHeight}
        getRowClassName={() => `${iotRatesTableClass}__row`}
        getRowHeight={() => rowHeight}
        getCellClassName={() => `${iotRatesTableClass}__cell`}
        Actions={getIOTRatesTableActions}
      />
    </div>
  );
};

IOTRatesTable.propTypes = {
  dispatchGetIOTRatesAction: PropTypes.func.isRequired,
  iotRatesFilters: PropTypes.instanceOf(Object),
};

IOTRatesTable.defaultProps = {
  iotRatesFilters: {},
};

export default IOTRatesTable;
