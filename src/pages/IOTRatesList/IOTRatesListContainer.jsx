import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Typography, Paper } from '@mui/material';
import { useAppContext } from 'AppContextProvider';
import { AiOutlineLineChart } from 'react-icons/ai';
import { HTTPService } from 'core/services/HTTPService';
import getIOTRatesFiltersAction from 'pages/IOTRatesList/GetIOTRatesFilters/actions';
import IOTRatesProvider from 'pages/IOTRatesList/IOTRatesContext';
import IOTRatesTableProvider from 'pages/IOTRatesList/IOTRatesTable/IOTRatesTableProvider';

import './IOTRatesListContainer.scss';

let iotRatesFiltersController = HTTPService.getController();

const IOTRatesListContainer = () => {
  const { primaryColor } = useAppContext();
  const dispatch = useDispatch();

  const dispatchGetIOTRatesFiltersAction = () => {
    HTTPService.cancelRequest(iotRatesFiltersController);
    iotRatesFiltersController = HTTPService.getController();

    dispatch(getIOTRatesFiltersAction(iotRatesFiltersController));
  };

  useEffect(() => {
    dispatchGetIOTRatesFiltersAction();
  }, []);

  return (
    <div className="iot-rates-list-container" id="iot-rates">
      <div className="iot-rates-list-main-header">
        <div className="iot-rates-list-main-header__info">
          <div className="iot-rates-list-main-header__icon">
            <AiOutlineLineChart color={primaryColor} size={30} />
          </div>
          <div className="iot-rates-list-main-header__title-wrap">
            <Typography
              variant="body2"
              component="h4"
              className="iot-rates-list-main-header__title"
            >
              IOT Rates
            </Typography>
            <Typography
              variant="body2"
              component="p"
              className="iot-rates-list-main-header__subtitle"
            >
              Select IOT Rate to change
            </Typography>
          </div>
        </div>
      </div>
      <Paper className="iot-rates-list-content">
        <IOTRatesProvider
          dispatchGetIOTRatesFiltersAction={dispatchGetIOTRatesFiltersAction}
        >
          <IOTRatesTableProvider />
        </IOTRatesProvider>
      </Paper>
    </div>
  );
};

export default IOTRatesListContainer;
