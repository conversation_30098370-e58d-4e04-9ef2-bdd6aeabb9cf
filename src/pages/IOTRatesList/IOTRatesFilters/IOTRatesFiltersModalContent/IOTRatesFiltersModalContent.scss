.iot-rates-filters-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .ps {
    max-height: 780px;
    padding-right: 20px;
  }

  &__row {
    margin-bottom: 32px;
    padding: 0 5px 0 0;

    .autocomplete {
      width: 100% !important;
    }

    @media (max-width: $small-mobile-width) {
      padding: 0 5px 0 0;
    }

    &.active {
      margin-bottom: 16px !important;

      .MuiFormGroup-root {
        flex-direction: row;
      }

      .MuiFormControlLabel-label {
        font-size: 12px !important;
      }

      .filter-title {
        margin-bottom: 2px !important;
        font-weight: 600;
      }
    }

    &.calendar {
      display: flex;
      flex-direction: row;
      gap: 20px;

      @media (max-width: $small-mobile-width) {
        flex-wrap: wrap;
      }
    }

    &.direction {
      display: flex;
      flex-flow: row wrap;
      gap: 10px;
    }

    .checkbox-group,
    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 4px;

      .MuiFormControlLabel-root {
        margin-right: 0;
      }

      .MuiFormControlLabel-label {
        font-size: 12px !important;
      }
    }
  }

  &__block {
    display: inline-flex;
    flex-direction: column;
    width: 100%;

    &:first-child {
      margin-right: 20px;
    }
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 30px;
    background-color: $white-color;
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    min-height: 80px;
    margin-bottom: 20px;

    &-wrap {
      display: flex;
      gap: 20px;
    }

    &-btn {
      width: 127px !important;
    }

    @media (max-width: $small-mobile-width) {
      flex-direction: column-reverse;
      row-gap: 10px;
      align-items: flex-start;
      padding: 50px 0;
    }
  }
}
