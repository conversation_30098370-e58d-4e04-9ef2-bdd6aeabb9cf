import React from 'react';
import PropTypes from 'prop-types';
import { Button } from '@mui/material';
import { AiOutlineClear } from 'react-icons/ai';
import {
  iotRatesFiltersModalContentActionsModifier,
  defaultIOTRatesFilters,
} from 'pages/IOTRatesList/IOTRatesFilters/constants';
import useIOTRatesFiltersAmount from 'pages/IOTRatesList/IOTRatesFilters/hooks/useIOTRatesFiltersAmount';
import { useIOTRatesContext } from 'pages/IOTRatesList/IOTRatesContext';

const Actions = ({
  closeModal,
  modalFilters,
}) => {
  const { setIotRatesFilters, iotRatesFilters } = useIOTRatesContext();
  const modalFiltersAmount = useIOTRatesFiltersAmount(
    modalFilters, iotRatesFilters,
  ).iotRatesFiltersAmount;

  const confirm = () => {
    setIotRatesFilters(modalFilters);
    closeModal();
  };

  const reset = () => {
    setIotRatesFilters(defaultIOTRatesFilters);
    closeModal();
  };

  return (
    <div className={iotRatesFiltersModalContentActionsModifier}>
      <div className={`${iotRatesFiltersModalContentActionsModifier}-wrap`}>
        <Button
          onClick={confirm}
          className={`${iotRatesFiltersModalContentActionsModifier}-btn`}
          variant="contained"
          color="primary"
          disabled={!modalFiltersAmount}
        >
          Confirm
        </Button>
      </div>
      <Button
        onClick={reset}
        startIcon={<AiOutlineClear />}
        variant="text"
        color="primary"
      >
        Reset Filtration
      </Button>
    </div>
  );
};

Actions.propTypes = {
  modalFilters: PropTypes.instanceOf(Object).isRequired,
  closeModal: PropTypes.func.isRequired,
};

export default Actions;
