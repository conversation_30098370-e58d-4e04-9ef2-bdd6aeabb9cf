import React, { useState } from 'react';
import PropTypes from 'prop-types';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';
import FiltersButton from 'shared/FiltersButton';
import useScrollLock from 'core/hooks/useScrollLock';
import IOTRatesFiltersModalContent from 'pages/IOTRatesList/IOTRatesFilters/IOTRatesFiltersModalContent';
import useIOTRatesFiltersAmount from 'pages/IOTRatesList/IOTRatesFilters/hooks/useIOTRatesFiltersAmount';
import { useIOTRatesContext } from 'pages/IOTRatesList/IOTRatesContext';
import {
  defaultIOTRatesFilters,
  iotRatesFiltersModalModifier,
} from 'pages/IOTRatesList/IOTRatesFilters/constants';

import './IOTRatesFilters.scss';

const IOTRatesFilters = ({
  isReadOnly,
}) => {
  const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);
  const { iotRatesFilters } = useIOTRatesContext();
  const { iotRatesFiltersAmount } = useIOTRatesFiltersAmount(
    iotRatesFilters, defaultIOTRatesFilters,
  );

  useScrollLock(isFiltersModalOpen);

  const openModal = () => {
    setIsFiltersModalOpen(true);
  };

  const closeModal = () => {
    setIsFiltersModalOpen(false);
  };

  const filtersButtonClassName = `iot-rates-filters__btn ${isReadOnly ? 'left' : ''}`;

  return (
    <>
      <FiltersButton
        filtersCounter={iotRatesFiltersAmount}
        className={filtersButtonClassName}
        openFiltersModal={openModal}
      />
      <CustomModal
        isOpen={isFiltersModalOpen}
        title="Filters"
        showButtons={false}
        handleOpen={setIsFiltersModalOpen}
        modalClass={iotRatesFiltersModalModifier}
        dataTestid={iotRatesFiltersModalModifier}
        container={() => document.getElementById('iot-rates')}
      >
        <IOTRatesFiltersModalContent
          isModalOpen={isFiltersModalOpen}
          closeModal={closeModal}
        />
      </CustomModal>
    </>
  );
};

IOTRatesFilters.propTypes = {
  isReadOnly: PropTypes.bool,
};

IOTRatesFilters.defaultProps = {
  isReadOnly: false,
};

export default IOTRatesFilters;
