import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import {
  FilterTitle,
  Calendar,
  MultipleAutocomplete,
  FilterRadioGroup,
  CheckboxFilter,
} from 'shared/TableFilters';
import {
  defaultIOTRatesFilters,
  iotRatesFiltersFields,
  iotRatesFiltersModalContentRowModifier,
  iotRatesFiltersModalContentBlockModifier,
} from 'pages/IOTRatesList/IOTRatesFilters/constants';
import {
  directionOptions,
  isPremiumOptions,
  typeOptions,
} from 'pages/IOTRatesList/IOTRatesTable/constants';
import { serviceTypeConfig } from 'core/contstants';
import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsFilterOptions
  from 'core/utilities/getOperatorsFilterOptions';

const Filters = ({
  modalFilters,
  setModalFilters,
}) => {
  const operatorsFilterOptions = getOperatorsFilterOptions();

  const iotRatesFiltersData = useSelector((state) => state.iotRatesFilters.data);
  const isIOTRatesFiltersLoading = useSelector((state) => state.iotRatesFilters.isLoading);
  const homeOperators = iotRatesFiltersData?.home_operators || [];
  const partnerOperators = iotRatesFiltersData?.partner_operators || [];
  const calledCountries = iotRatesFiltersData?.called_countries || [];
  const currencies = iotRatesFiltersData?.currency_codes || [];
  const types = iotRatesFiltersData?.types || [];
  const isPremium = iotRatesFiltersData?.is_premium || [];

  return (
    <>
      <div className={iotRatesFiltersModalContentRowModifier}>
        <FilterTitle title="OPERATORS" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={iotRatesFiltersFields.homeOperators}
          availableOptions={homeOperators}
          placeholder="Home Operators"
          isLoading={isIOTRatesFiltersLoading}
          getOptionLabel={(option) => (option.pmn_code ? option.pmn_code : '')}
          optionKey="pmn_code"
          filterOptions={operatorsFilterOptions}
          getLabel={(option) => <OperatorsLabel operator={option} />}
        />
      </div>
      <div className={iotRatesFiltersModalContentRowModifier}>
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={iotRatesFiltersFields.partnerOperators}
          availableOptions={partnerOperators}
          placeholder="Partner Operators"
          isLoading={isIOTRatesFiltersLoading}
          getOptionLabel={(option) => (option.pmn_code ? option.pmn_code : '')}
          optionKey="pmn_code"
          filterOptions={operatorsFilterOptions}
          getLabel={(option) => <OperatorsLabel operator={option} />}
        />
      </div>
      <div className={`${iotRatesFiltersModalContentRowModifier} calendar`}>
        <span className={iotRatesFiltersModalContentBlockModifier}>
          <FilterTitle title="START DATE" />
          <Calendar
            modalFilters={modalFilters}
            setModalFilters={setModalFilters}
            startDateField={iotRatesFiltersFields.startDateMin}
            endDateField={iotRatesFiltersFields.startDateMax}
            minDate={iotRatesFiltersData?.start_date_min}
            maxDate={iotRatesFiltersData?.start_date_max}
            isLoading={isIOTRatesFiltersLoading}
          />
        </span>
        <span className={iotRatesFiltersModalContentBlockModifier}>
          <FilterTitle title="END DATE" />
          <Calendar
            modalFilters={modalFilters}
            setModalFilters={setModalFilters}
            startDateField={iotRatesFiltersFields.endDateMin}
            endDateField={iotRatesFiltersFields.endDateMax}
            minDate={iotRatesFiltersData?.end_date_min}
            maxDate={iotRatesFiltersData?.end_date_max}
            isLoading={isIOTRatesFiltersLoading}
          />
        </span>
      </div>
      <div className={`${iotRatesFiltersModalContentRowModifier} active`}>
        <FilterTitle title="TYPES" />
        <div className="checkbox-group">
          {(types.length ? types.map((type) => ({
            value: type,
            title: typeOptions.find((option) => option.value === type)?.title || type,
          })) : typeOptions).map((option) => (
            <CheckboxFilter
              key={option.value}
              modalFilters={modalFilters}
              setModalFilters={setModalFilters}
              filterField={iotRatesFiltersFields.types}
              filterValue={option.value}
              filterLabel={option.title}
            />
          ))}
        </div>
      </div>
      <div className={`${iotRatesFiltersModalContentRowModifier} active`}>
        <FilterTitle title="TRAFFIC DIRECTION" />
        <div className="checkbox-group">
          {directionOptions.map((option) => (
            <CheckboxFilter
              key={option.value}
              modalFilters={modalFilters}
              setModalFilters={setModalFilters}
              filterField={iotRatesFiltersFields.directions}
              filterValue={option.value}
              filterLabel={option.title}
            />
          ))}
        </div>
      </div>
      <div className={`${iotRatesFiltersModalContentRowModifier} active`}>
        <FilterTitle title="PREMIUM" />
        <FilterRadioGroup
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          availableValues={isPremium}
          filterField={iotRatesFiltersFields.isPremium}
          filterConfig={isPremiumOptions}
          groupClassName="is-premium-radio-group"
        />
      </div>
      <div className={iotRatesFiltersModalContentRowModifier}>
        <FilterTitle title="SERVICE TYPES" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={iotRatesFiltersFields.serviceTypes}
          availableOptions={serviceTypeConfig.map((option) => option.value)}
          optionsConfig={serviceTypeConfig}
          placeholder="Service Types"
        />
      </div>
      <div className={iotRatesFiltersModalContentRowModifier}>
        <FilterTitle title="CALLED COUNTRIES" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={iotRatesFiltersFields.calledCountries}
          availableOptions={calledCountries.map((country) => country.id)}
          optionsConfig={calledCountries.map((country) => ({
            title: `${country.name} (${country.code})`,
            value: country.id,
          }))}
          placeholder="Called Countries"
          isLoading={isIOTRatesFiltersLoading}
        />
      </div>
      <div className={iotRatesFiltersModalContentRowModifier}>
        <FilterTitle title="CURRENCIES" />
        <MultipleAutocomplete
          modalFilters={modalFilters}
          setModalFilters={setModalFilters}
          field={iotRatesFiltersFields.currencies}
          availableOptions={currencies}
          optionsConfig={currencies.map((currency) => ({ title: currency, value: currency }))}
          placeholder="Currencies"
          isLoading={isIOTRatesFiltersLoading}
        />
      </div>
    </>
  );
};

Filters.propTypes = {
  modalFilters: PropTypes.instanceOf(Object),
  setModalFilters: PropTypes.func,
};

Filters.defaultProps = {
  modalFilters: defaultIOTRatesFilters,
  setModalFilters: () => {},
};

export default Filters;
