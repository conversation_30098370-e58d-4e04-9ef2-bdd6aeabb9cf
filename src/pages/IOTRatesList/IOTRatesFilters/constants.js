import { ALL } from 'pages/IOTRatesList/IOTRatesTable/constants';

export const iotRatesFiltersModalModifier = 'iot-rates-filters-modal';
export const iotRatesFiltersModalContentModifier = 'iot-rates-filters-modal-content';
export const iotRatesFiltersModalContentRowModifier = `${iotRatesFiltersModalContentModifier}__row`;
export const iotRatesFiltersModalContentBlockModifier = `${iotRatesFiltersModalContentModifier}__block`;
export const iotRatesFiltersModalContentActionsModifier = `${iotRatesFiltersModalContentModifier}__actions`;

export const iotRatesFiltersFields = {
  homeOperators: 'home_operators',
  partnerOperators: 'partner_operators',
  directions: 'traffic_directions',
  serviceTypes: 'service_types',
  calledCountries: 'called_countries',
  isPremium: 'is_premium',
  startDateMin: 'start_date_min',
  startDateMax: 'start_date_max',
  endDateMin: 'end_date_min',
  endDateMax: 'end_date_max',
  types: 'types',
  currencies: 'currency_codes',
};

export const defaultIOTRatesFilters = {
  [iotRatesFiltersFields.homeOperators]: [],
  [iotRatesFiltersFields.partnerOperators]: [],
  [iotRatesFiltersFields.directions]: [],
  [iotRatesFiltersFields.serviceTypes]: [],
  [iotRatesFiltersFields.calledCountries]: [],
  [iotRatesFiltersFields.isPremium]: ALL,
  [iotRatesFiltersFields.startDateMin]: '',
  [iotRatesFiltersFields.startDateMax]: '',
  [iotRatesFiltersFields.endDateMin]: '',
  [iotRatesFiltersFields.endDateMax]: '',
  [iotRatesFiltersFields.types]: [],
  [iotRatesFiltersFields.currencies]: [],
};
