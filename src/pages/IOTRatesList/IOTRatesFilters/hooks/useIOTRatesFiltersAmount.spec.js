import { renderHook } from '@testing-library/react';

import {
  defaultIOTRatesFilters,
  iotRatesFiltersFields,
} from 'pages/IOTRatesList/IOTRatesFilters/constants';

import { ALL } from 'pages/IOTRatesList/IOTRatesTable/constants';

import useIOTRatesFiltersAmount from './useIOTRatesFiltersAmount';

describe('IOTRatesList: IOTRatesFilters: hooks: useIOTRatesFiltersAmount', () => {
  test('should initialize iotRatesFiltersAmount to 0 when filters match defaults', () => {
    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      defaultIOTRatesFilters,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(0);
  });

  test('should count array filters when they have values', () => {
    const filtersWithArrayValues = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.homeOperators]: [1, 2],
      [iotRatesFiltersFields.serviceTypes]: ['VOICE_MO'],
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithArrayValues,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(2);
  });

  test('should count string filters when they differ from defaults', () => {
    const filtersWithStringValues = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.startDateMin]: '2024-01-01',
      [iotRatesFiltersFields.endDateMax]: '2024-12-31',
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithStringValues,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(2);
  });

  test('should count empty string filters when they differ from defaults', () => {
    const filtersWithEmptyString = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: '',
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithEmptyString,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(1);
  });

  test('should count boolean filters when they differ from defaults', () => {
    const filtersWithBooleanValues = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: true,
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithBooleanValues,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(1);
  });

  test('should count multiple different filter types', () => {
    const filtersWithMultipleValues = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.homeOperators]: [1],
      [iotRatesFiltersFields.isPremium]: '',
      [iotRatesFiltersFields.startDateMin]: '2024-01-01',
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithMultipleValues,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(3);
  });

  test('should not count empty arrays', () => {
    const filtersWithEmptyArrays = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.homeOperators]: [],
      [iotRatesFiltersFields.serviceTypes]: [],
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersWithEmptyArrays,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(0);
  });

  test('should not count string filters that match defaults', () => {
    const filtersMatchingDefaults = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: ALL,
      [iotRatesFiltersFields.startDateMin]: '',
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      filtersMatchingDefaults,
      defaultIOTRatesFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(0);
  });

  test('should handle scenario: select "-", then select "All" - should show 1 change', () => {
    const currentAppliedFilters = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: '',
    };

    const modalFiltersBackToAll = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: ALL,
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      modalFiltersBackToAll,
      currentAppliedFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(1);
  });

  test('should handle scenario: select "-", then select "-" again - should show 0 changes', () => {
    const currentAppliedFilters = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: '',
    };

    const modalFiltersStillDash = {
      ...defaultIOTRatesFilters,
      [iotRatesFiltersFields.isPremium]: '',
    };

    const { result } = renderHook(() => useIOTRatesFiltersAmount(
      modalFiltersStillDash,
      currentAppliedFilters,
    ));

    expect(result.current.iotRatesFiltersAmount).toBe(0);
  });
});
