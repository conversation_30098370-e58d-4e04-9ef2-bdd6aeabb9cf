import { useMemo } from 'react';
import { isEqual } from 'lodash';

const useIOTRatesFiltersAmount = (
  iotRatesFilters,
  initialIOTRatesFilters,
) => {
  const iotRatesFiltersAmount = useMemo(() => {
    let amount = 0;

    Object.keys(iotRatesFilters).forEach((key) => {
      const initialValue = initialIOTRatesFilters[key];
      const currentValue = iotRatesFilters[key];

      if (Array.isArray(currentValue) && currentValue.length) {
        amount += 1;
      } else if (
        typeof currentValue === 'string'
        && !isEqual(initialValue, currentValue)
      ) {
        amount += 1;
      } else if (
        typeof currentValue === 'boolean'
        && !isEqual(initialValue, currentValue)
      ) {
        amount += 1;
      }
    });

    return amount;
  }, [iotRatesFilters, initialIOTRatesFilters]);

  return {
    iotRatesFiltersAmount,
  };
};

export default useIOTRatesFiltersAmount;
