import React from 'react';

import { Provider } from 'react-redux';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { AppContextProvider } from 'AppContextProvider';
import { configureStore } from '@reduxjs/toolkit';

import rootReducer from 'core/rootReducer';
import FiltersButton from './FiltersButton';

describe('shared: FiltersButton:', () => {
  const store = configureStore({ reducer: rootReducer });
  const defaultProps = {
    filtersTitle: 'Title',
    openFiltersModal: jest.fn(),
    filtersCounter: 3,
  };
  const btnTestId = 'filters-btn';
  const getFiltersButton = (props) => (
    <Provider store={store}>
      <AppContextProvider>
        <FiltersButton {...props} {...defaultProps} />
      </AppContextProvider>
    </Provider>
  );

  test('should be FiltersButton in the DOM', () => {
    const { getByTestId } = render(getFiltersButton());
    const filtersButton = getByTestId(btnTestId);

    expect(filtersButton).toBeInTheDocument();
  });

  test('should call action when user click button', () => {
    const { getByTestId } = render(getFiltersButton());
    const filtersButton = getByTestId(btnTestId);

    fireEvent.click(filtersButton);

    expect(defaultProps.openFiltersModal).toBeCalled();
  });

  test('should be title in the DOM', () => {
    const { getByText, getByTestId } = render(getFiltersButton());
    const filtersButton = getByTestId(btnTestId);

    fireEvent.mouseOver(filtersButton);

    waitFor(() => {
      expect(getByText(defaultProps.filtersTitle)).toBeInTheDocument();
    });
  });

  test('should be counter in the DOM', () => {
    const { getByText } = render(getFiltersButton());
    const filtersCounter = getByText(defaultProps.filtersCounter);

    expect(filtersCounter).toBeInTheDocument();
  });

  test('should have correct class for button when filtersCounter is provided', () => {
    const { getByTestId } = render(getFiltersButton());

    const button = getByTestId('filters-btn');

    expect(button.className).toContain('filters-btn_with-counter');
  });
});
