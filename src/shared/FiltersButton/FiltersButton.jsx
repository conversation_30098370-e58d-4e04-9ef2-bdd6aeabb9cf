import React from 'react';
import PropTypes from 'prop-types';
import { AiOutlineFilter } from 'react-icons/ai';

import { Button, Tooltip } from '@mui/material';

import { useAppContext } from 'AppContextProvider';
import FiltersButtonTooltipTitle from './FiltersButtonTooltipTitle';

const FiltersButton = ({
  filtersTitle, openFiltersModal, filtersCounter, className, globalFilters, isCountryViewSelect,
}) => {
  const { primaryColor, getBrandColors } = useAppContext();

  const showFiltersInTooltip = filtersCounter && globalFilters;

  const tooltipTitle = showFiltersInTooltip ? (
    <FiltersButtonTooltipTitle
      globalFilters={globalFilters}
      isCountryViewSelect={isCountryViewSelect}
    />
  ) : (
    filtersTitle
  );

  const buttonModifier = `${className} filters-btn ${filtersCounter ? 'filters-btn_with-counter' : ''}`;

  return (
    <Tooltip title={tooltipTitle} arrow placement={showFiltersInTooltip ? 'left' : 'top'}>
      <Button
        className={buttonModifier}
        variant="text"
        data-testid="filters-btn"
        onClick={openFiltersModal}
        sx={{
          bgcolor: getBrandColors(primaryColor)[50],
          '&.MuiButtonBase-root:hover': {
            bgcolor: getBrandColors(primaryColor)[100],
          },
        }}
      >
        <AiOutlineFilter size={19} color={primaryColor} />
        {!!filtersCounter && (
        <div className="filters-btn__counter" data-testid="filters-btn__counter" style={{ backgroundColor: primaryColor }}>
          {filtersCounter}
        </div>
        )}
      </Button>
    </Tooltip>
  );
};

FiltersButton.propTypes = {
  className: PropTypes.string,
  filtersTitle: PropTypes.string,
  openFiltersModal: PropTypes.func,
  filtersCounter: PropTypes.number,
  globalFilters: PropTypes.shape({
    partner_operators: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
    home_operators: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
    partner_countries: PropTypes.arrayOf(PropTypes.instanceOf(Object)),
    start_date: PropTypes.string,
    end_date: PropTypes.string,
  }),
  isCountryViewSelect: PropTypes.bool,
};

FiltersButton.defaultProps = {
  className: '',
  filtersTitle: 'Filters',
  openFiltersModal: () => {},
  filtersCounter: null,
  globalFilters: null,
  isCountryViewSelect: false,
};

export default FiltersButton;
