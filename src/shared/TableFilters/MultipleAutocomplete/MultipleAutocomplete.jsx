import React from 'react';
import PropTypes from 'prop-types';
import { TextField } from '@mui/material';

import Autocomplete from 'shared/Autocomplete';

const limitTags = 3;

const MultipleAutocomplete = ({
  titleKey,
  valueKey,
  modalFilters,
  setModalFilters,
  availableOptions,
  optionsConfig,
  field,
  placeholder,
  ...props
}) => {
  const onAutocompleteChange = (value) => {
    setModalFilters((state) => ({
      ...state,
      [field]: value,
    }));
  };

  const getOptionTitle = (option) => optionsConfig.find(
    (item) => option === item[valueKey],
  )?.[titleKey];

  return (
    <Autocomplete
      className="autocomplete"
      data-testid="autocomplete"
      autoComplete={false}
      selectOnFocus={false}
      limitTags={limitTags}
      options={availableOptions}
      getLabel={getOptionTitle}
      getOptionLabel={(option) => getOptionTitle(option)}
      value={modalFilters?.[field] || []}
      onChange={(e, value) => onAutocompleteChange(value)}
      renderInput={(params) => <TextField {...params} placeholder={placeholder} />}
      multiple
      {...props}
    />
  );
};

MultipleAutocomplete.propTypes = {
  modalFilters: PropTypes.instanceOf(Object),
  setModalFilters: PropTypes.func,
  field: PropTypes.string,
  availableOptions: PropTypes.arrayOf(PropTypes.string),
  optionsConfig: PropTypes.arrayOf(PropTypes.shape({
    title: PropTypes.string,
    value: PropTypes.string,
  })),
  titleKey: PropTypes.string,
  valueKey: PropTypes.string,
  placeholder: PropTypes.string,
};

MultipleAutocomplete.defaultProps = {
  optionsConfig: [],
  modalFilters: {},
  setModalFilters: () => {},
  field: '',
  availableOptions: [],
  titleKey: 'title',
  valueKey: 'value',
  placeholder: '',
};

export default MultipleAutocomplete;
