export const getDateRangeFilters = (tableFilters, globalFilters) => ({
  start_date_max: tableFilters?.start_date_max || globalFilters?.end_date,
  start_date_min: tableFilters?.start_date_min || globalFilters?.start_date,
  end_date_min: tableFilters?.end_date_min || globalFilters?.start_date,
  end_date_max: tableFilters?.end_date_max || globalFilters?.end_date,
});

export const getDateRangeFiltersFromFilterAPI = (tableFilters, filtersData) => ({
  start_date_max: tableFilters?.start_date_max || filtersData?.start_date_max,
  start_date_min: tableFilters?.start_date_min || filtersData?.start_date_min,
  end_date_min: tableFilters?.end_date_min || filtersData?.end_date_min,
  end_date_max: tableFilters?.end_date_max || filtersData?.end_date_max,
});

export default { getDateRangeFilters, getDateRangeFiltersFromFilterAPI };
