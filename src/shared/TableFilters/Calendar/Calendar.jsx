import React from 'react';
import PropTypes from 'prop-types';

import dayjs from 'dayjs';

import RangePicker from 'shared/RangePicker';

import FilterTitle from 'shared/TableFilters/FilterTitle';

const Calendar = ({
  modalFilters,
  setModalFilters,
  startDateField,
  endDateField,
  title,
  minDate,
  maxDate,
  ...props
}) => {
  const onCalendarChange = (calendarValues) => {
    setModalFilters((state) => ({
      ...state,
      ...calendarValues,
    }));
  };

  return (
    <>
      {title && <FilterTitle title={title} />}
      <RangePicker
        startDate={modalFilters?.[startDateField]}
        endDate={modalFilters?.[endDateField]}
        onCalendarChange={(startDate, endDate) => onCalendarChange({
          [startDateField]: startDate,
          [endDateField]: endDate,
        })}
        minDate={minDate ? dayjs(minDate) : undefined}
        maxDate={maxDate ? dayjs(maxDate) : undefined}
        placeholder={['From', 'To']}
        disabled={!minDate && !maxDate}
        allowEmpty={!minDate && !maxDate}
        {...props}
      />
    </>
  );
};

Calendar.propTypes = {
  modalFilters: PropTypes.instanceOf(Object),
  setModalFilters: PropTypes.func,
  startDateField: PropTypes.string,
  endDateField: PropTypes.string,
  title: PropTypes.string,
  minDate: PropTypes.string,
  maxDate: PropTypes.string,
};

Calendar.defaultProps = {
  modalFilters: {},
  setModalFilters: () => {},
  startDateField: '',
  endDateField: '',
  title: '',
  minDate: '',
  maxDate: '',
};

export default Calendar;
