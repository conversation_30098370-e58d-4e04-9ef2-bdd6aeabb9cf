import { getDateRangeFilters, getDateRangeFiltersFromFilterAPI } from './utilities';

describe('shared: TableFilters: getDateRangeFilters', () => {
  test('should return tableFilters values when they are provided', () => {
    const tableFilters = {
      start_date_max: '2024-12-31',
      start_date_min: '2024-01-01',
      end_date_min: '2024-06-01',
      end_date_max: '2024-12-01',
    };
    const globalFilters = {
      start_date: '2023-01-01',
      end_date: '2023-12-31',
    };

    const result = getDateRangeFilters(tableFilters, globalFilters);

    expect(result).toEqual({
      start_date_max: '2024-12-31',
      start_date_min: '2024-01-01',
      end_date_min: '2024-06-01',
      end_date_max: '2024-12-01',
    });
  });

  test('should fall back to globalFilters when tableFilters are not provided', () => {
    const tableFilters = {};
    const globalFilters = {
      start_date: '2023-01-01',
      end_date: '2023-12-31',
    };

    const result = getDateRangeFilters(tableFilters, globalFilters);

    expect(result).toEqual({
      start_date_max: '2023-12-31',
      start_date_min: '2023-01-01',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    });
  });

  test('should handle missing globalFilters gracefully', () => {
    const tableFilters = {
      start_date_max: '2024-12-31',
    };
    const globalFilters = {};

    const result = getDateRangeFilters(tableFilters, globalFilters);

    expect(result).toEqual({
      start_date_max: '2024-12-31',
      start_date_min: undefined,
      end_date_min: undefined,
      end_date_max: undefined,
    });
  });

  test('should handle missing tableFilters gracefully', () => {
    const tableFilters = null;
    const globalFilters = {
      start_date: '2023-01-01',
      end_date: '2023-12-31',
    };

    const result = getDateRangeFilters(tableFilters, globalFilters);

    expect(result).toEqual({
      start_date_max: '2023-12-31',
      start_date_min: '2023-01-01',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    });
  });

  test('should handle both inputs being null or undefined', () => {
    const result = getDateRangeFilters(null, null);

    expect(result).toEqual({
      start_date_max: undefined,
      start_date_min: undefined,
      end_date_min: undefined,
      end_date_max: undefined,
    });
  });
});

describe('shared: TableFilters: getDateRangeFiltersFromFilterAPI', () => {
  test('should return tableFilters values when they are provided', () => {
    const tableFilters = {
      start_date_max: '2024-12-31',
      start_date_min: '2024-01-01',
      end_date_min: '2024-06-01',
      end_date_max: '2024-12-01',
    };
    const filtersData = {
      start_date_min: '2023-01-01',
      start_date_max: '2023-12-31',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    };

    const result = getDateRangeFiltersFromFilterAPI(tableFilters, filtersData);

    expect(result).toEqual({
      start_date_max: '2024-12-31',
      start_date_min: '2024-01-01',
      end_date_min: '2024-06-01',
      end_date_max: '2024-12-01',
    });
  });

  test('should fall back to filtersData when tableFilters are not provided', () => {
    const tableFilters = {};
    const filtersData = {
      start_date_min: '2023-01-01',
      start_date_max: '2023-12-31',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    };

    const result = getDateRangeFiltersFromFilterAPI(tableFilters, filtersData);

    expect(result).toEqual({
      start_date_max: '2023-12-31',
      start_date_min: '2023-01-01',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    });
  });

  test('should handle missing filtersData gracefully', () => {
    const tableFilters = {
      start_date_max: '2024-12-31',
    };
    const filtersData = {};

    const result = getDateRangeFiltersFromFilterAPI(tableFilters, filtersData);

    expect(result).toEqual({
      start_date_max: '2024-12-31',
      start_date_min: undefined,
      end_date_min: undefined,
      end_date_max: undefined,
    });
  });

  test('should handle missing tableFilters gracefully', () => {
    const tableFilters = null;
    const filtersData = {
      start_date_min: '2023-01-01',
      start_date_max: '2023-12-31',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    };

    const result = getDateRangeFiltersFromFilterAPI(tableFilters, filtersData);

    expect(result).toEqual({
      start_date_max: '2023-12-31',
      start_date_min: '2023-01-01',
      end_date_min: '2023-01-01',
      end_date_max: '2023-12-31',
    });
  });

  test('should handle both inputs being null or undefined', () => {
    const result = getDateRangeFiltersFromFilterAPI(null, null);

    expect(result).toEqual({
      start_date_max: undefined,
      start_date_min: undefined,
      end_date_min: undefined,
      end_date_max: undefined,
    });
  });
});
