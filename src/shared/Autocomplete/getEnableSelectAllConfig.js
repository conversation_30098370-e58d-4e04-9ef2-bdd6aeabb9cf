export const selectAllOptionConfig = {
  id: 'select-all',
  label: 'Select All',
  modifier: 'selected-all-option',
};

const getEnableSelectAllConfig = ({
  value, optionKey, options,
}) => {
  const isAllSelected = value?.length === options?.length;
  const selectAllOption = optionKey
    ? { id: selectAllOptionConfig.id, [optionKey]: selectAllOptionConfig.label }
    : selectAllOptionConfig.label;
  const isSelectAllOption = (option) => (optionKey
    ? option?.id === selectAllOption.id
    : option === selectAllOptionConfig.label);

  const optionsWhenEnableSelectAll = [selectAllOption, ...options];

  const getNewValueWhenEnableSelectAll = (newValues) => {
    const isSelectAllClicked = newValues?.some(isSelectAllOption);

    if (isSelectAllClicked) {
      const currentRegularSelected = value.filter((item) => !isSelectAllOption(item));
      const unselectClicked = currentRegularSelected.length === options.length;

      return unselectClicked ? [] : options;
    }

    return newValues;
  };

  const getOptionClassName = (option) => `${option[optionKey] === selectAllOptionConfig.label || option === selectAllOptionConfig.label ? selectAllOptionConfig.modifier : ''}`;

  return {
    isSelectAllOption,
    isAllSelected,
    optionsWhenEnableSelectAll,
    getNewValueWhenEnableSelectAll,
    getOptionClassName,
  };
};

export default getEnableSelectAllConfig;
