import React from 'react';
import { fireEvent, render, within } from '@testing-library/react';
import Autocomplete from './index';
import { mockAutocompleteOptions, autocompleteModifier } from './constants';

describe('shared: Autocomplete', () => {
  const mockOnAutocompleteChange = jest.fn();
  const defaultProps = {
    options: mockAutocompleteOptions,
    value: mockAutocompleteOptions[0],
    onChange: mockOnAutocompleteChange,
  };
  const autocomplete = <Autocomplete {...defaultProps} />;

  test('should be autocomplete in the DOM', async () => {
    const { getByTestId } = render(autocomplete);

    const autocompleteEl = getByTestId(autocompleteModifier);

    expect(autocompleteEl).toBeInTheDocument();
  });

  test('should be mock options in the DOM', async () => {
    const { getByTestId } = render(autocomplete);
    const autocompleteEl = getByTestId(autocompleteModifier);
    const input = within(autocompleteEl).getByRole('combobox');
    const mockOptionVal = mockAutocompleteOptions[0].label;

    autocompleteEl.focus();
    fireEvent.change(input, { target: { value: mockOptionVal } });
    fireEvent.keyDown(autocompleteEl, { key: 'ArrowDown' });
    fireEvent.keyDown(autocompleteEl, { key: 'Enter' });

    expect(input).toHaveValue(mockOptionVal);
  });

  test('should call mock onChange function after changing autocomplete', async () => {
    const { getByTestId } = render(autocomplete);
    const autocompleteEl = getByTestId(autocompleteModifier);
    const input = within(autocompleteEl).getByRole('combobox');
    const mockOptionVal = mockAutocompleteOptions[1].label;

    autocompleteEl.focus();
    fireEvent.change(input, { target: { value: mockOptionVal } });
    fireEvent.keyDown(autocompleteEl, { key: 'ArrowDown' });
    fireEvent.keyDown(autocompleteEl, { key: 'Enter' });

    expect(mockOnAutocompleteChange).toBeCalled();
  });
});
