import getEnableSelectAllConfig, { selectAllOptionConfig } from './getEnableSelectAllConfig';

describe('shared: Autocomplete: getEnableSelectAllConfig', () => {
  describe('when optionKey is not provided (simple values)', () => {
    const baseOptions = ['A', 'B', 'C'];
    const selectAllLabel = selectAllOptionConfig.label;

    test('should return optionsWithSelectAll with Select All at the beginning', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [],
        options: baseOptions,
      });

      expect(optionsWhenEnableSelectAll).toEqual([selectAllLabel, ...baseOptions]);
    });

    test('should detect Select All option correctly', () => {
      const { isSelectAllOption } = getEnableSelectAllConfig({ value: [], options: baseOptions });

      expect(isSelectAllOption('Select All')).toBe(true);
      expect(isSelectAllOption('A')).toBe(false);
    });

    test('should return all options when Select All is clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['A', 'Select All']);

      expect(result).toEqual(baseOptions);
    });

    test('should return empty array if Select All is clicked again when all options are already selected', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: baseOptions,
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['Select All']);

      expect(result).toEqual([]);
    });

    test('should return unchanged value when Select All is not clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['A', 'B']);

      expect(result).toEqual(['A', 'B']);
    });

    test('should set isAllSelected correctly', () => {
      const { isAllSelected } = getEnableSelectAllConfig({
        value: baseOptions,
        options: baseOptions,
      });

      expect(isAllSelected).toBe(true);
    });
  });

  describe('when optionKey is provided (object values)', () => {
    const objectOptions = [
      { id: '1', name: 'A' },
      { id: '2', name: 'B' },
    ];

    const selectAllObj = {
      id: 'select-all',
      name: 'Select All',
    };

    test('should include selectAll option with optionKey', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [],
        options: objectOptions,
        optionKey: 'name',
      });

      expect(optionsWhenEnableSelectAll[0]).toEqual(selectAllObj);
    });

    test('should detect Select All object correctly', () => {
      const { isSelectAllOption } = getEnableSelectAllConfig({
        value: [],
        options: objectOptions,
        optionKey: 'name',
      });

      expect(isSelectAllOption(selectAllObj)).toBe(true);
      expect(isSelectAllOption(objectOptions[0])).toBe(false);
    });

    test('should return all options when Select All is clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [objectOptions[0]],
        options: objectOptions,
        optionKey: 'name',
      });

      const result = getNewValueWhenEnableSelectAll([selectAllObj, objectOptions[0]]);

      expect(result).toEqual(objectOptions);
    });

    test('should clear all when deselecting Select All', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: objectOptions,
        options: objectOptions,
        optionKey: 'name',
      });

      const result = getNewValueWhenEnableSelectAll([selectAllObj]);

      expect(result).toEqual([]);
    });

    test('should return the modifier class for "Select All" when no optionKey is used', () => {
      const options = ['Option 1'];
      const config = getEnableSelectAllConfig({ value: [], optionKey: null, options });

      const className = config.getOptionClassName('Select All');

      expect(className).toBe(selectAllOptionConfig.modifier);
    });

    test('should return an empty string for regular options', () => {
      const options = [{ id: '1', label: 'Option 1' }];
      const config = getEnableSelectAllConfig({ value: [], optionKey: 'label', options });

      const className = config.getOptionClassName(options[0]);

      expect(className).toBe('');
    });
  });
});
