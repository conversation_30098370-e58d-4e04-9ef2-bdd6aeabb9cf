import React from 'react';
import PropTypes from 'prop-types';

import {
  Chip,
} from '@mui/material';

const MultipleAutocompleteTags = ({
  tagValue,
  getTagProps,
  optionKey,
  getOptionLabel,
  ...props
}) => tagValue.map((option, index) => (
  <Chip
    label={option[optionKey] || getOptionLabel(option)}
    {...getTagProps({ index })}
    key={option[optionKey] || getOptionLabel(option)}
    {...props}
  />
));

MultipleAutocompleteTags.propTypes = {
  tagValue: PropTypes.instanceOf(Array),
  getTagProps: PropTypes.func,
  optionKey: PropTypes.string,
};

MultipleAutocompleteTags.defaultProps = {
  tagValue: [],
  getTagProps: () => '',
  optionKey: '',
  getOptionLabel: () => {},
};

export default MultipleAutocompleteTags;
