import React from 'react';

import {
  render, screen, fireEvent,
} from '@testing-library/react';

import MultipleAutocompleteTagsWithLimitation from './MultipleAutocompleteTagsWithLimitation';

describe('shared: Autocomplete: MultipleAutocompleteTagsWithLimitation', () => {
  const mockOptions = [
    { id: '1', name: 'Item 1' },
    { id: '2', name: 'Item 2' },
    { id: '3', name: 'Item 3' },
    { id: '4', name: 'Item 4' },
    { id: '5', name: 'Item 5' },
  ];

  const mockGetTagProps = ({ index }) => ({
    'data-testid': `chip-${index}`,
    onDelete: jest.fn(),
    key: `chip-key-${index}`,
  });

  const mockGetOptionLabel = (option) => option.name;

  test('should render correctly', () => {
    const tagValue = mockOptions;

    render(
      <MultipleAutocompleteTagsWithLimitation
        tagValue={tagValue}
        getTagProps={mockGetTagProps}
        getOptionLabel={mockGetOptionLabel}
      />,
    );

    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('+3')).toBeInTheDocument();
  });

  test('should display correct tooltip content on hover', async () => {
    const tagValue = mockOptions;
    const expectedTooltipContent = mockOptions.map((opt) => opt.name).join(', ');

    render(
      <MultipleAutocompleteTagsWithLimitation
        tagValue={tagValue}
        getTagProps={mockGetTagProps}
        getOptionLabel={mockGetOptionLabel}
        optionKey="name"
      />,
    );

    const elementTriggeringTooltip = screen.getByText('Item 1');

    fireEvent.mouseOver(elementTriggeringTooltip);

    const tooltipElement = await screen.findByRole('tooltip', { name: expectedTooltipContent });

    expect(tooltipElement).toBeInTheDocument();
  });

  test('should use optionKey for labels if provided', () => {
    const tagValue = [{ customKey: 'Generic Item A' }, { customKey: 'Generic Item B' }];
    const optionKey = 'customKey';

    render(
      <MultipleAutocompleteTagsWithLimitation
        tagValue={tagValue}
        limitTags={1}
        optionKey={optionKey}
        getTagProps={mockGetTagProps}
        getOptionLabel={() => ''}
      />,
    );

    expect(screen.getByText('Generic Item A')).toBeInTheDocument();
    expect(screen.getByText('+1')).toBeInTheDocument();
  });

  test('should use getOptionLabel if optionKey value is undefined or null', async () => {
    const tagValue = [
      { id: 'x1', label: 'Fallback Item A' },
      { id: 'x2', label: 'Fallback Item B' },
      { id: 'x3', name: 'Fallback Item C' },
    ];
    const expectedTooltipContent = 'Fallback Item A, Fallback Item B, Fallback Item C';

    render(
      <MultipleAutocompleteTagsWithLimitation
        tagValue={tagValue}
        limitTags={1}
        getTagProps={mockGetTagProps}
        getOptionLabel={(option) => option.label || option.name}
      />,
    );

    const elementTriggeringTooltip = screen.getByText('Fallback Item A');

    fireEvent.mouseOver(elementTriggeringTooltip);

    const tooltipElement = await screen.findByRole('tooltip', { name: expectedTooltipContent });

    expect(tooltipElement).toBeInTheDocument();
    expect(screen.getByText('Fallback Item A')).toBeInTheDocument();
    expect(screen.getByText('+2')).toBeInTheDocument();
  });
});
