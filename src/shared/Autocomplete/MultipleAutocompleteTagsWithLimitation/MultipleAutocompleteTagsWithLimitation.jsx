import React from 'react';
import PropTypes from 'prop-types';

import {
  Chip,
  Tooltip,
} from '@mui/material';

const MultipleAutocompleteTagsWithLimitation = ({
  tagValue,
  limitTags,
  optionKey,
  getTagProps,
  getOptionLabel,
}) => {
  const tooltipContent = tagValue.map((val) => val[optionKey] || getOptionLabel(val)).join(', ');
  const displayCount = tagValue.length;

  return (
    <Tooltip
      placement="top"
      arrow
      title={tooltipContent}
      className="multiple-autocomplete-tags-with-limitation"
    >
      <span>
        {tagValue.slice(0, limitTags).map((option, index) => (
          <Chip
            label={option[optionKey] || getOptionLabel(option)}
            {...getTagProps({ index })}
            key={option[optionKey] || getOptionLabel(option)}
          />
        ))}
        <Chip
          label={`+${displayCount - limitTags}`}
          {...getTagProps({ index: limitTags })}
          key="additional"
        />
      </span>
    </Tooltip>
  );
};

MultipleAutocompleteTagsWithLimitation.propTypes = {
  tagValue: PropTypes.instanceOf(Array),
  limitTags: PropTypes.number,
  optionKey: PropTypes.string,
  getTagProps: PropTypes.func,
  getOptionLabel: PropTypes.func,
};

MultipleAutocompleteTagsWithLimitation.defaultProps = {
  tagValue: [],
  optionKey: '',
  getTagProps: () => {},
  limitTags: 2,
  getOptionLabel: () => '',
};

export default MultipleAutocompleteTagsWithLimitation;
