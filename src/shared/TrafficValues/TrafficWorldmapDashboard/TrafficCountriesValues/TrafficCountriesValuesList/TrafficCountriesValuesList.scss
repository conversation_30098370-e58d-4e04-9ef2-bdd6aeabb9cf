.traffic-countries-values-list {
  min-width: 386px;
  font-size: 14px;
  color: $dark-color-500;
  padding-top: 37px;
  margin-left: 20px;
  padding-right: 15px;

  @media (max-width: $tablet-width) {
    padding-top: 0;
  }

  @media (max-width: $small-mobile-width) {
    min-width: 310px;
    max-width: fit-content;
    margin-left: 15px;
    padding-right: 15px;
    overflow: hidden;
  }

  &__wrap {
    -webkit-overflow-scrolling: touch;

    @media (max-width: $tablet-width) {
      overflow-x: auto;
    }

    &::-webkit-scrollbar {
      height: 7px;
      background-color: transparent !important;
      appearance: none;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent !important;
    }

    &::-webkit-scrollbar-thumb {
      background-color: $light-color-300 !important;
      border-radius: 30px !important;
    }
  }

  &__head {
    display: flex;
    justify-content: space-between;
    padding-right: 15px;

    &-item {
      margin-right: 40px;
      width: 200px;
      text-align: right;

      &:last-child {
        margin-right: 10px;
      }

      &-name {
        font-weight: 700 !important;

        @media (max-width: $tablet-width) {
          font-size: 12px !important;
        }
      }

      &-parent-name {
        font-size: 12px !important;
        color: $dark-color-300 !important;
        margin-bottom: 15px !important;
        display: block !important;
      }
    }

    &.comparison {
      width: 100%;
      padding: 0 17px;

      h2 {
        width: 190px;
      }
    }
  }

  &__name {
    font-size: 18px !important;

    @media (max-width: $small-mobile-width) {
      font-size: 16px !important;
    }

    &-addition {
      color: $dark-color-300;

      @media (max-width: $tablet-width) {
        font-size: 12px !important;
      }
    }
  }

  &__item {
    border: 1px solid $border-color;
    border-radius: 4px;
    margin-bottom: 10px;

    &-name {
      display: inline-flex;
      width: 150px;
    }

    &:hover {
      border-color: $white-color;
      box-shadow: $shadow8;
      cursor: pointer;

      .traffic-countries-values-list__item-name {
        font-weight: bold;
      }
    }

    &-value {
      white-space: nowrap;
      margin-right: 40px;
      width: 200px;
      text-align: right;

      &:last-child {
        margin-right: 10px;
      }
    }

    &-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 40px;
      padding: 0 18px;
    }

    &-flag {
      width: 30px;
      height: fit-content;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      margin-right: 10px;
      box-shadow: $shadow8;

      &-wrap {
        display: flex;
        align-items: center;
      }

      img {
        height: 100%;
        width: 100%;
        display: flex;
        border-radius: 2px;
      }
    }
  }

  .scrollbar-container {
    height: 100%;
    max-height: 460px;
    margin-top: 29px;
    padding-right: 15px;
  }

  .list-preloader__i {
    height: 50px !important;
  }

  .error-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;

    &__text {
      text-align: center;
    }

    &__icon-wrap {
      margin-top: 148px;
    }
  }
}
