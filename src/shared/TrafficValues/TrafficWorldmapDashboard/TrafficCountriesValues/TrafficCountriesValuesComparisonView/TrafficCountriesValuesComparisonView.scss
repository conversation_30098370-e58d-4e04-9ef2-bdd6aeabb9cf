.map-comparison {
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  padding: 0;

  @media (max-width: $tablet-width) {
    flex-direction: column;
  }

  .MuiTabs-flexContainer {
    width: auto;
    margin-right: 20px;
  }

  .MuiButtonBase-root {
    align-items: flex-start !important;
    min-width: 120px !important;
  }

  .MuiTabs-scroller {
    align-items: flex-start;
    justify-content: flex-start;

    @media (max-width: $tablet-width) {
      margin-right: 15px !important;
      margin-left: 15px !important;
    }
  }

  .traffic-dashboard-tabs__title {
    @media (max-width: $tablet-width) {
      font-size: 12px !important;
    }
  }

  .traffic-dashboard-tabs__tab-panel {
    width: 100%;
    padding: 0;
  }

  .traffic-countries-values-worldmap {
    width: auto;
    padding-top: 10px;
  }

  .traffic-countries-values-worldmap__legend {
    width: 170px;
  }

  & > svg {
    width: calc(100% - 170px);
  }
}

.traffic-dashboard-tabs.comparison {
  & > .MuiTabs-root {
    .MuiButtonBase-root {
      width: 82px;
    }
  }

  .traffic-countries-values-list {
    padding-top: 20px;
    max-width: 1400px;
    padding-right: 15px;

    @media (max-width: $tablet-width) {
      max-width: 1200px;
      min-width: fit-content;
      overflow: hidden;
    }

    .scrollbar-container {
      max-height: 400px;
    }
  }

  .error-placeholder {
    align-items: center;
    margin-top: 60px;
  }

  .no-data__text {
    width: 318px;
    text-align: center;
  }
}
