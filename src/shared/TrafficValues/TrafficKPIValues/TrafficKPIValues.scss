$less-desktop: 1340px;

.traffic-kpi-values {
  width: 100%;
  display: flex;
  flex-direction: column;

  &__loading {
    padding: 0 15px;
  }

  &__header {
    padding: 0 15px;
  }

  &__chart {
    display: grid;
    grid-template-columns: 1.39fr minmax(300px, 1fr);
    margin: 15px 0 0;
    padding: 0 15px;
    grid-template-areas: "chart charges";

    &_comparison {
      display: grid;
      margin: 15px 0 0;
      padding: 0 15px;
      grid-template-columns: minmax(220px, 0.5fr) 1.39fr minmax(300px, 1fr);
      grid-template-areas: "title chart charges";
      gap: 10px;

      @media (max-width: $less-desktop) {
        grid-template-columns: 1fr 1fr;
        row-gap: 25px;
        grid-template-areas:
          "title title"
          "chart charges";
      }

      @media (max-width: $mobile-width) {
        grid-template-areas:
          "title title"
          "chart chart"
          "charges charges";
      }
    }

    @media (max-width: $tablet-width) {
      grid-template-columns: 1fr;
      grid-template-areas:
        "chart"
        "charges";
    }
  }

  .export-traffic-values-button {
    position: absolute;
    right: 15px;

    @media (max-width: $small-mobile-width) {
      position: static;
      margin-top: 10px;
      margin-right: 15px;
    }
  }

  .error-placeholder {
    align-items: center;
    justify-content: center;
  }
}
