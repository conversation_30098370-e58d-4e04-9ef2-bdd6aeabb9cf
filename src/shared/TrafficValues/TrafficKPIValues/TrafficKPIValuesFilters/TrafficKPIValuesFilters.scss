$less-small-mobile: 640px;

.traffic-kpi-values-filters {
  display: inline-flex;
  align-items: center;
  padding: 0 15px;

  @media (max-width: $small-tablet-width) {
    margin-top: 10px;
  }

  @media (max-width: $small-tablet-width) {
    gap: 10px;

    .charge-type-autocomplete, .volume-type-autocomplete {
      width: 100px !important;
    }

    .currencies-autocomplete {
      width: 115px !important;
    }
  }

  @media (max-width: $mobile-width) {
    flex-direction: column;
    align-items: flex-start;
    row-gap: 15px;

    .charge-type-autocomplete, .volume-type-autocomplete, .currencies-autocomplete {
      width: 130px !important;
    }

    @media (max-width: $less-small-mobile) {
      width: 100%;

      .charge-type-autocomplete, .volume-type-autocomplete, .currencies-autocomplete {
        width: 60% !important;
      }
    }
  }

  .charge-type-autocomplete__wrap {
    margin: 0 20px !important;
    display: flex;
    align-items: center;

    @media (max-width: $small-tablet-width) {
      margin-left: 0 !important;
    }

    @media (max-width: $less-small-mobile) {
      width: 100%;
      gap: 20px;
    }
  }

  .volume-type-autocomplete__wrap {
    margin-left: 50px !important;
    display: flex;
    align-items: center;

    @media (max-width: $small-tablet-width) {
      margin-left: 20px !important;
    }

    @media (max-width: $small-tablet-width) {
      margin-left: 0 !important;
    }

    @media (max-width: $less-small-mobile) {
      width: 100%;
      gap: 17px;
    }
  }

  &__items {
    display: flex;
    align-items: center;

    @media (max-width: $small-tablet-width) {
      gap: 15px;
    }

    @media (max-width: $less-small-mobile) {
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      row-gap: 15px;
    }
  }

  &__range-picker {
    &-wrap {
      display: flex;
      align-items: center;
      gap: 30px;

      @media (max-width: $less-small-mobile) {
        gap: 50px;
      }
    }

    &-title {
      font-weight: bold !important;
      display: none;

      @media (max-width: $mobile-width) {
        display: block;
      }
    }
  }

  &__currencies {
    &-wrap {
      display: flex;
      align-items: center;

      @media (max-width: $less-small-mobile) {
        width: 100%;
        gap: 20px;
      }
    }

    &-title {
      font-weight: bold !important;
      display: none;
      margin-right: 5px !important;

      @media (max-width: $less-small-mobile) {
        display: block;
      }
    }
  }
}
