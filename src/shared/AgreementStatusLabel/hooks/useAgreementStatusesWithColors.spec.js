import { renderHook } from '@testing-library/react';
import { useAppContext } from 'AppContextProvider';
import useAgreementStatusesWithColors from './useAgreementStatusesWithColors';

jest.mock('AppContextProvider', () => ({
  useAppContext: jest.fn(),
}));

jest.mock('@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss', () => ({
  greenColor500: '#359B70',
  yellowColor500: '#F3C73C',
  redColor500: '#DA5454',
  blueColor500: '#0000FF',
  darkColor300: '#555770',
  darkColor500: '#555555',
  tealColor200: '#A9EFF2',
  tealColor500: '#00B7C4',
  orangeColor400: '#FF8800',
  brandBlueColor300: '#6E6BCF',
}));

describe('shared: AgreementStatusLabel: hooks: useAgreementStatusesWithColors: ', () => {
  const mockColors = {
    green: '#359B70',
    yellow: '#F3C73C',
    red: '#DA5454',
    dark300: '#555770',
    dark500: '#555555',
    blue: '#0000FF',
    orange400: '#FF8800',
    teal200: '#A9EFF2',
    teal500: '#00B7C4',
    brand300: '#6E6BCF',
  };

  useAppContext.mockImplementation(() => ({
    getBrandColors: () => ({
      300: mockColors.blue,
      100: mockColors.dark300,
    }),
  }));

  test('should returned agreement statuses and correct colors', () => {
    const { result } = renderHook(() => useAgreementStatusesWithColors());

    const expectedResults = [
      { label: 'Live', value: 'LIVE', color: mockColors.green },
      { label: 'Draft', value: 'DRAFT', color: mockColors.dark300 },
      { label: 'In Review', value: 'IN_REVIEW', color: mockColors.yellow },
      { label: 'Approved', value: 'APPROVED', color: mockColors.blue },
      { label: 'Closed', value: 'CLOSED', color: mockColors.dark300 },
      { label: 'Rejected', value: 'REJECTED', color: mockColors.red },
      { label: 'Submitted', value: 'SUBMITTED', color: mockColors.brand300 },
      { label: 'Submit failed', value: 'SUBMIT_FAILED', color: mockColors.orange400 },
      { label: 'Budgeting', value: 'BUDGETING', color: mockColors.teal200 },
      { label: 'Auto-renewed', value: 'AUTO_RENEWED', color: mockColors.teal500 },
    ];

    expect(result.current).toEqual(expectedResults);
  });
});
