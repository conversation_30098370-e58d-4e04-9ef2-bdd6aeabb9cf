import styles
  from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import { useAppContext } from 'AppContextProvider';
import appStyles from 'assets/styles/variables.module.scss';
import agreementStatuses from 'shared/AgreementStatusLabel/constants';

const useAgreementStatuses = () => {
  const { getBrandColors } = useAppContext();

  const blueColor = getBrandColors(styles.blueColor500)[300];
  const greyColor = getBrandColors(styles.darkColor500)[100];

  return [
    {
      label: 'Live',
      value: agreementStatuses.life,
      color: styles.greenColor500,
    },
    {
      label: 'Draft',
      value: agreementStatuses.draft,
      color: greyColor,
    },
    {
      label: 'In Review',
      value: agreementStatuses.inReview,
      color: styles.yellowColor500,
    },
    {
      label: 'Approved',
      value: agreementStatuses.approved,
      color: blueColor,
    },
    {
      label: 'Closed',
      value: agreementStatuses.closed,
      color: styles.darkColor300,
    },
    {
      label: 'Rejected',
      value: agreementStatuses.rejected,
      color: styles.redColor500,
    },
    {
      label: 'Submitted',
      value: agreementStatuses.submitted,
      color: appStyles.brandBlueColor300,
    },
    {
      label: 'Submit failed',
      value: agreementStatuses.submitFailed,
      color: appStyles.orangeColor400,
    },
    {
      label: 'Budgeting',
      value: agreementStatuses.budgeting,
      color: appStyles.tealColor200,
    },
    {
      label: 'Auto-renewed',
      value: agreementStatuses.autoRenewed,
      color: appStyles.tealColor500,
    },
  ];
};

export default useAgreementStatuses;
