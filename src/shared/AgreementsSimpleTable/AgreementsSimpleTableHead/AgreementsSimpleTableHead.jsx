import React from 'react';
import { TableCell, TableHead, TableRow } from '@mui/material';
import { modifiers } from 'shared/AgreementsSimpleTable/constants';
import PropTypes from 'prop-types';

const AgreementsSimpleTableHead = ({
  periodColumnName, isActionCompleted, operationStatusTitle, showCheckbox, showBudget,
}) => (
  <TableHead className={modifiers.tableHead} data-testid={modifiers.tableHead}>
    <TableRow className={modifiers.tableRow}>
      {showCheckbox && (
        <TableCell className={modifiers.tableCell}>
          {/* Checkbox header */}
        </TableCell>
      )}
      <TableCell className={modifiers.tableCell}>
        ID
      </TableCell>
      {showBudget && (
        <TableCell className={modifiers.tableCell}>
          Budget
        </TableCell>
      )}
      {isActionCompleted && (
        <TableCell className={modifiers.tableCell}>
          {operationStatusTitle}
        </TableCell>
      )}
      <TableCell className={modifiers.tableCell}>
        Agreement reference
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Home Operators
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Partner Operators
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Partner Countries
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        {periodColumnName}
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Status
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Negotiator
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Active
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Added/Changed
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Applied
      </TableCell>
      <TableCell className={modifiers.tableCell}>
        Calculation status
      </TableCell>
    </TableRow>
  </TableHead>
);

AgreementsSimpleTableHead.propTypes = {
  isActionCompleted: PropTypes.bool,
  periodColumnName: PropTypes.string,
  operationStatusTitle: PropTypes.string,
  showCheckbox: PropTypes.bool,
  showBudget: PropTypes.bool,
};

AgreementsSimpleTableHead.defaultProps = {
  isActionCompleted: false,
  periodColumnName: 'Period',
  operationStatusTitle: '',
  showCheckbox: false,
  showBudget: false,
};

export default AgreementsSimpleTableHead;
