import React from 'react';
import { render, screen } from '@testing-library/react';
import BudgetDescription from './BudgetDescription';

describe('shared: BudgetDescription', () => {
  test('should render the description text', () => {
    render(<BudgetDescription description="This is a test description." />);

    expect(screen.getByText('This is a test description.')).toBeInTheDocument();
  });

  test('should apply default clamp value (3)', () => {
    render(<BudgetDescription description="Test" />);

    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  test('should apply the custom clamp value', () => {
    render(<BudgetDescription description="Clamped text" clamp={2} />);

    expect(screen.getByText('Clamped text')).toBeInTheDocument();
  });
});
