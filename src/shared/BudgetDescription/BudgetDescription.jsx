import React from 'react';
import Dotdotdot from 'react-dotdotdot';
import { Typography } from '@mui/material';
import PropTypes from 'prop-types';

const BudgetDescription = ({
  description,
  clamp,
  variant,
  ...typographyProps
}) => (
  <Typography variant={variant} {...typographyProps} component="div">
    <Dotdotdot clamp={clamp}>
      {description}
    </Dotdotdot>
  </Typography>
);

BudgetDescription.propTypes = {
  description: PropTypes.string,
  clamp: PropTypes.number,
  variant: PropTypes.string,
};

BudgetDescription.defaultProps = {
  description: '',
  clamp: 3,
  variant: 'body2',
};

export default BudgetDescription;
