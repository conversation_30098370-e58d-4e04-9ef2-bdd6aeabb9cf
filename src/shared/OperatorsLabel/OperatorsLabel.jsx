import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@mui/material';
import './OperatorsLabel.scss';
import operatorsLabelThemeMode from './constants';

const OperatorsLabel = ({ operator, themeMode }) => (
  <div className="operators-label">
    <Typography variant="body2" className={`operators-label__code ${themeMode}`}>
      {operator.pmn_code}
    </Typography>
    <Typography variant="body2" className={`operators-label__name ${themeMode}`}>
      {operator.name}
    </Typography>
  </div>
);

OperatorsLabel.propTypes = {
  operator: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
    pmn_code: PropTypes.string.isRequired,
    name: PropTypes.string,
  }).isRequired,
  themeMode: PropTypes.oneOf([operatorsLabelThemeMode.dark, operatorsLabelThemeMode.light]),
};

OperatorsLabel.defaultProps = {
  themeMode: operatorsLabelThemeMode.dark,
};

export default OperatorsLabel;
