.kpi-chart-bar {
  width: 90%;
  height: 100px;
  grid-area: chart;

  @media (max-width: $tablet-width) {
    width: 99%;
  }

  @media (max-width: $mobile-width) {
    border-top: none;
    padding-top: 0;
  }

  &-tooltip {
    border-radius: 4px;
    background-color: $dark-color-300;
    position: relative;
    border-bottom: 1px dotted $dark-color-300;
    min-width: 232px;
    height: 75px;
    padding: 8px 12px;

    &__wrap {
      display: flex;
      align-items: center;
      color: $white-color;
    }

    &__title {
      margin: 0 2px 0 10px;
    }

    &__bullet {
      height: 10px;
      width: 10px;
      border-radius: 50%;
      z-index: 200;
    }

    &__value {
      margin-left: auto;
      font-weight: 700;
    }

    &::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -10px;
      border-width: 10px;
      border-style: solid;
      border-color: $dark-color-300 transparent transparent transparent;
    }
  }

  .yAxis {
    display: none;
  }

  tspan {
    fill: $dark-color-300;
    font-size: 12px;
  }

  .recharts-tooltip-wrapper {
    z-index: 4;
  }
}
