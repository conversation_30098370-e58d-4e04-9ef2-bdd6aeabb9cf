import React from 'react';
import PropTypes from 'prop-types';

import { Tooltip, Typography, useMediaQuery } from '@mui/material';
import Dotdotdot from 'react-dotdotdot';
import styles
  from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const EntityName = ({ name, transitionPoint }) => {
  const isTransitionPoint = useMediaQuery(`(max-width: ${transitionPoint})`);

  return isTransitionPoint
    ? (
      <Tooltip
        key={name}
        title={name}
        placement="top"
        arrow
      >
        <Typography variant="h3" component="h3" className="entity-parameters-name">
          <Dotdotdot clamp={1}>{name}</Dotdotdot>
        </Typography>
      </Tooltip>
    )
    : <Typography variant="h3" component="h3" className="entity-parameters-name"><Dotdotdot clamp={2}>{name}</Dotdotdot></Typography>;
};

EntityName.propTypes = {
  name: PropTypes.string,
  transitionPoint: PropTypes.string,
};

EntityName.defaultProps = {
  name: '',
  transitionPoint: styles.mobileWidth,
};

export default EntityName;
