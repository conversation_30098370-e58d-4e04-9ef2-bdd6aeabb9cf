import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { AppContextProvider } from 'AppContextProvider';
import rootReducer from 'core/rootReducer';
import AgreementsTransferTable from './AgreementsTransferTable';

const mockAgreements = [
  {
    id: 1,
    name: 'Agreement 1',
    budget_name: 'Budget 1',
    home_operators: [{ id: 1, pmn_code: 'A' }],
    partner_operators: [{ id: 1, pmn_code: 'B' }],
    partner_countries: [{ name: 'Country1' }],
    start_date: '2022-01-01',
    end_date: '2022-12-31',
    status: 'Approved',
    negotiator: { name: 'John' },
    is_active: true,
    updated_at: '2022-06-01',
    applied_at: '2022-07-01',
    calculation_status: 'Applied',
  },
  {
    id: 2,
    name: 'Agreement 2',
    budget_name: 'Budget 2',
    home_operators: [{ id: 2, pmn_code: 'C' }],
    partner_operators: [{ id: 2, pmn_code: 'D' }],
    partner_countries: [{ name: 'Country2' }],
    start_date: '2022-01-01',
    end_date: '2022-12-31',
    status: 'Approved',
    negotiator: { name: 'Jane' },
    is_active: false,
    updated_at: '2022-06-02',
    applied_at: '2022-07-02',
    calculation_status: 'Pending',
  },
];

describe('AgreementsTransferTable', () => {
  const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
      serializableCheck: false,
    }),
  });

  const renderWithProvider = (component) => (
    <Provider store={store}>
      <AppContextProvider>
        {component}
      </AppContextProvider>
    </Provider>
  );

  it('renders all columns and agreement data', () => {
    render(renderWithProvider(
      <AgreementsTransferTable
        agreements={mockAgreements}
        onRemove={() => {}}
        currentAgreementId={1}
      />,
    ));
    expect(screen.getByText('Agreement 1')).toBeInTheDocument();
    expect(screen.getByText('Agreement 2')).toBeInTheDocument();
    expect(screen.getByText('Budget 1')).toBeInTheDocument();
    expect(screen.getByText('Budget 2')).toBeInTheDocument();
  });

  it('calls onSelectionChange when checkbox is clicked', () => {
    const onSelectionChange = jest.fn();
    render(renderWithProvider(
      <AgreementsTransferTable
        agreements={mockAgreements}
        onSelectionChange={onSelectionChange}
        currentAgreementId={1}
        selectedAgreementIds={[]}
      />,
    ));
    const checkboxes = screen.getAllByRole('checkbox');
    // Click the checkbox for agreement 2 (agreement 1 should be disabled as current)
    fireEvent.click(checkboxes[1]);
    expect(onSelectionChange).toHaveBeenCalledWith([2]);
  });

  it('disables checkbox for the current agreement', () => {
    render(renderWithProvider(
      <AgreementsTransferTable
        agreements={[mockAgreements[0]]}
        currentAgreementId={1}
      />,
    ));
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeDisabled();
    expect(checkbox).toBeChecked();
  });

  it('renders nothing if agreements list is empty', () => {
    render(renderWithProvider(
      <AgreementsTransferTable
        agreements={[]}
        onRemove={() => {}}
        currentAgreementId={1}
      />,
    ));
    expect(screen.queryByText('Agreement 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Agreement 2')).not.toBeInTheDocument();
  });
});
