import React from 'react';
import PerfectScrollbar from 'react-perfect-scrollbar';
import {
  Table,
  TableContainer,
  TableBody,
  TableCell,
  TableRow,
  Checkbox,
  Box,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';
import Marker from 'shared/Marker';
import AgreementsSimpleTableHead from 'shared/AgreementsSimpleTable/AgreementsSimpleTableHead';
import AgreementStatusLabel from 'shared/AgreementStatusLabel';
import FormattedOperators from 'shared/FormattedOperators';
import AgreementActivationStatusIcon from 'shared/AgreementActivationStatusIcon';
import { getPeriodForPreview } from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/utilities';
import { getFormattedDate } from 'core/utilities/getFormattedDate';
import CalculationStatus from 'shared/CalculationStatus';
import { modifiers } from 'shared/AgreementsSimpleTable/constants';

const AgreementsTransferTable = ({
  agreements,
  currentAgreementId,
  selectedAgreementIds = [],
  onSelectionChange,
  agreementsWithMarkers = [],
}) => {
  const getChangedDate = (date) => (date ? getFormattedDate(date) : '-');

  const handleSelectionChange = (agreementId, isChecked) => {
    // Current agreement cannot be unchecked
    if (String(agreementId) === String(currentAgreementId)) return;

    let newSelection;
    if (isChecked) {
      newSelection = [...selectedAgreementIds, agreementId];
    } else {
      newSelection = selectedAgreementIds.filter((id) => String(id) !== String(agreementId));
    }
    onSelectionChange(newSelection);
  };

  const getMarkerColor = (agreementId) => {
    const agreementWithMarker = agreementsWithMarkers.find((a) => a.value?.id === agreementId);
    return agreementWithMarker?.markerColor || '#000';
  };

  return (
    <>
      <Typography
        component="h3"
        variant="h3"
        mb={4}
      >
        Agreements for Comparison
      </Typography>
      <TableContainer className={modifiers.tableWrap}>
        <PerfectScrollbar>
          <Table className={modifiers.table}>
            <AgreementsSimpleTableHead showCheckbox showBudget />
            <TableBody className={modifiers.tableBody}>
              {agreements.map((row) => (
                <TableRow key={`${row.name}-${row.id}`} className={modifiers.tableRow}>
                  <TableCell className={modifiers.tableCell}>
                    <Checkbox
                      checked={
                        selectedAgreementIds.some((id) => String(id) === String(row.id))
                        || String(row.id) === String(currentAgreementId)
                      }
                      onChange={(e) => handleSelectionChange(row.id, e.target.checked)}
                      disabled={String(row.id) === String(currentAgreementId)}
                    />
                  </TableCell>
                  <TableCell className={modifiers.tableCell}>{row.id}</TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '200px' }}>{row.budget_name || '-'}</TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '358px' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Marker backgroundColor={getMarkerColor(row.id)} />
                      {row.name}
                    </Box>
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '210px' }}>
                    <FormattedOperators data={row.home_operators} keyName="pmn_code" />
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '250px' }}>
                    <FormattedOperators data={row.partner_operators} keyName="pmn_code" />
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '310px' }}>
                    <FormattedOperators data={row.partner_countries} keyName="name" />
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '180px' }}>
                    {getPeriodForPreview(row.start_date, row.end_date)}
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '108px' }}>
                    <AgreementStatusLabel statusValue={row.status} />
                  </TableCell>
                  <TableCell className={modifiers.tableCell}>{row.negotiator?.name || '-'}</TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '108px' }}>
                    <AgreementActivationStatusIcon isActive={row.is_active} />
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '160px' }}>
                    {getChangedDate(row?.updated_at)}
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '160px' }}>
                    {getChangedDate(row?.applied_at)}
                  </TableCell>
                  <TableCell className={modifiers.tableCell} sx={{ width: '180px' }}>
                    <CalculationStatus calculationStatus={row.calculation_status} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </PerfectScrollbar>
      </TableContainer>
    </>
  );
};

AgreementsTransferTable.propTypes = {
  agreements: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  currentAgreementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  selectedAgreementIds: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  onSelectionChange: PropTypes.func,
  agreementsWithMarkers: PropTypes.arrayOf(PropTypes.shape({})),
};

AgreementsTransferTable.defaultProps = {
  selectedAgreementIds: [],
  onSelectionChange: () => {},
  agreementsWithMarkers: [],
};

export default AgreementsTransferTable;
