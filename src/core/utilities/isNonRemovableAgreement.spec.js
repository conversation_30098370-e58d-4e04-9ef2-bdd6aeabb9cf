import agreementStatuses from 'shared/AgreementStatusLabel/constants';
import isNonRemovableAgreement from './isNonRemovableAgreement';

describe('isNonRemovableAgreement', () => {
  const nonRemovableCases = [
    [true, agreementStatuses.life],
    [true, agreementStatuses.approved],
    [true, agreementStatuses.closed],
    [true, agreementStatuses.submitted],
    [true, agreementStatuses.budgeting],
    [true, agreementStatuses.autoRenewed],
  ];

  const removableCases = [
    [false, agreementStatuses.draft],
    [false, agreementStatuses.inReview],
    [false, agreementStatuses.rejected],
    [false, agreementStatuses.submitFailed],
  ];

  const unknownCases = [
    [false, undefined],
    [false, 'UNKNOWN_STATUS'],
  ];

  test.each(nonRemovableCases)('should return %s for non-removable status %s', (expected, status) => {
    expect(isNonRemovableAgreement(status)).toBe(expected);
  });

  test.each(removableCases)('should return %s for removable status %s', (expected, status) => {
    expect(isNonRemovableAgreement(status)).toBe(expected);
  });

  test.each(unknownCases)('should return %s for unknown or undefined status %s', (expected, status) => {
    expect(isNonRemovableAgreement(status)).toBe(expected);
  });
});
