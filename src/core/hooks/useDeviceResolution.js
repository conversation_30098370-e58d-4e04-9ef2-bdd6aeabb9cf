import { useMediaQuery } from '@mui/material';

import appStyles from 'assets/styles/variables.module.scss';

const useDeviceResolution = ({
  smallDesktop = appStyles.smallDesktopWidth,
  tablet = appStyles.tabletWidth,
  smallTablet = appStyles.smallTabletWidth,
  mobile = appStyles.mobileWidth,
  smallMobile = appStyles.smallMobileWidth,
} = {}) => {
  const isSmallDesktop = useMediaQuery(`(max-width:${smallDesktop})`);
  const isTablet = useMediaQuery(`(max-width:${tablet})`);
  const isSmallTablet = useMediaQuery(`(max-width:${smallTablet})`);
  const isMobile = useMediaQuery(`(max-width:${mobile})`);
  const isSmallMobile = useMediaQuery(`(max-width:${smallMobile})`);

  return {
    isSmallDesktop,
    isTablet,
    isSmallTablet,
    isMobile,
    isSmallMobile,
  };
};

export default useDeviceResolution;
