import { renderHook } from '@testing-library/react';

import useDeviceResolution from './useDeviceResolution';

jest.mock('assets/styles/variables.module.scss', () => ({
  smallDesktopWidth: '1535px',
  tabletWidth: '1199px',
  smallTabletWidth: '991px',
  mobileWidth: '767px',
  smallMobileWidth: '575px',
}));

const createMatchMedia = (width) => (query) => {
  const match = query.match(/\(max-width:\s*(\d+)px\)/);
  const maxWidth = match ? parseInt(match[1], 10) : 0;
  return {
    matches: width <= maxWidth,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  };
};

const smallMobileWidth = 500;
const mobileWidth = 700;
const tabletWidth = 1100;
const smallTabletWidth = 990;
const smallDesktopWidth = 1500;

describe('core: hooks: useDeviceResolution', () => {
  const originalMatchMedia = window.matchMedia;

  afterEach(() => {
    window.matchMedia = originalMatchMedia;
  });

  test.each([
    {
      screenType: 'Small Mobile',
      width: smallMobileWidth,
      expected: {
        isSmallDesktop: true,
        isTablet: true,
        isSmallTablet: true,
        isMobile: true,
        isSmallMobile: true,
      },
    },
    {
      screenType: 'Mobile',
      width: mobileWidth,
      expected: {
        isSmallDesktop: true,
        isTablet: true,
        isSmallTablet: true,
        isMobile: true,
        isSmallMobile: false,
      },
    },
    {
      screenType: 'Small Tablet',
      width: smallTabletWidth,
      expected: {
        isSmallDesktop: true,
        isTablet: true,
        isSmallTablet: true,
        isMobile: false,
        isSmallMobile: false,
      },
    },
    {
      screenType: 'Tablet',
      width: tabletWidth,
      expected: {
        isSmallDesktop: true,
        isTablet: true,
        isSmallTablet: false,
        isMobile: false,
        isSmallMobile: false,
      },
    },
    {
      screenType: 'Small Desktop',
      width: smallDesktopWidth,
      expected: {
        isSmallDesktop: true,
        isTablet: false,
        isSmallTablet: false,
        isMobile: false,
        isSmallMobile: false,
      },
    },
  ])(
    'should return correct values for $screenType screen size',
    ({
      width, expected,
    }) => {
      window.matchMedia = createMatchMedia(width);

      const { result } = renderHook(() => useDeviceResolution());

      expect(result.current).toEqual(expected);
    },
  );
});
