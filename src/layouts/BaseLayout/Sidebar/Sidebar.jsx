import React from 'react';
import {
  useLocation,
} from 'react-router-dom';

import { paths } from 'core/configs/paths';

import { AiOutlineDollar, AiOutlineLineChart } from 'react-icons/ai';

import SharedSidebar from '@nv2/nv2-pkg-js-shared-components/lib/Sidebar';

import useContainerAppContextProvider from 'core/hooks/useContainerAppContextProvider';
import { useAppContext } from 'AppContextProvider';

const Sidebar = () => {
  const { primaryColor, secondaryColor, getBrandColors } = useAppContext();
  const {
    isSidebarToggled,
    toggleSidebar,
    isSidebarCollapsed,
  } = useContainerAppContextProvider();

  const location = useLocation();
  const currentRoutes = location.pathname.split('/');
  const activeLink = currentRoutes[1];

  const sidebarConfig = [
    {
      id: 'budgets',
      name: 'Budgets',
      path: paths.budgetList,
      icon: <AiOutlineDollar style={{ fontSize: '24px' }} className="sidebar__nav-link-icon" />,
      submenu: [{
        id: 'budgets-list',
        name: 'Budget List',
        path: paths.budgetList,
      },
      ],
    },
    {
      id: 'iot-rates',
      name: 'IOT Rates',
      path: paths.iotRatesList,
      icon: <AiOutlineLineChart style={{ fontSize: '24px' }} className="sidebar__nav-link-icon" />,
      submenu: [{
        id: 'iot-rates-list',
        name: 'IOT Rates List',
        path: paths.iotRatesList,
      },
      ],
    },
  ];

  return (
    <SharedSidebar
      activeLink={activeLink}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      getCurrentThemeColors={getBrandColors}
      sidebarConfig={sidebarConfig}
      appName="Budgets"
      collapsed={isSidebarCollapsed}
      toggleSidebar={toggleSidebar}
      toggled={isSidebarToggled}
      showTooltip={false}
    />
  );
};

export default Sidebar;
