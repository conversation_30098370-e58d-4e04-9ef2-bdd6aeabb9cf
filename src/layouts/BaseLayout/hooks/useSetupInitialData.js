import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import getCurrenciesAction
  from 'features/CurrenciesAutocomplete/GetCurrencies/actions';
import getClientCurrencyAction from 'features/GetClientCurrency/actions';
import getPredefinedFiltersAction from 'features/GetPredefinedFilters/actions';

const useSetupInitialData = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const setupInitialData = () => {
      dispatch(getCurrenciesAction());
      dispatch(getClientCurrencyAction());
      dispatch(getPredefinedFiltersAction());
    };

    setupInitialData();
  }, []);
};

export default useSetupInitialData;
