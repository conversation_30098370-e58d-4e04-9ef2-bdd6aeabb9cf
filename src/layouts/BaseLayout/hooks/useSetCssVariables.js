import { useLayoutEffect } from 'react';

import { useAppContext } from 'AppContextProvider';
import cssVariables from 'core/cssVariables';

const useSetCssVariables = () => {
  const { primaryColor, secondaryColor, getBrandColors } = useAppContext();

  useLayoutEffect(() => {
    const brandPrimaryColors = getBrandColors(primaryColor);
    const brandSecondaryColors = getBrandColors(secondaryColor);

    document.documentElement.style.setProperty(
      cssVariables.brandBlueColor50,
      brandPrimaryColors[50],
    );
    document.documentElement.style.setProperty(
      cssVariables.brandBlueColor500,
      brandPrimaryColors[500],
    );
    document.documentElement.style.setProperty(
      cssVariables.brandYellowColor500,
      brandSecondaryColors[500],
    );
  }, []);
};

export default useSetCssVariables;
