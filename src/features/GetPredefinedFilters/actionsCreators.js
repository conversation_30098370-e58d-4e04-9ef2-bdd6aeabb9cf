import {
  GET_PREDEFINED_FILTERS_REQUEST,
  GET_PREDEFINED_FILTERS_SUCCESS,
  GET_PREDEFINED_FILTERS_FAILURE,
} from './actionTypes';

export const getPredefinedFiltersRequest = () => (
  { type: GET_PREDEFINED_FILTERS_REQUEST });
export const getPredefinedFiltersSuccess = (data) => (
  { type: GET_PREDEFINED_FILTERS_SUCCESS, data });
export const getPredefinedFiltersFailure = (error) => (
  { type: GET_PREDEFINED_FILTERS_FAILURE, error });
