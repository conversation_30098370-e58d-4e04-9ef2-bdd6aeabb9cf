import {
  getPredefinedFiltersRequest,
  getPredefinedFiltersSuccess,
  getPredefinedFiltersFailure,
} from './actionsCreators';
import getPredefinedFilters from './api.service';

const getPredefinedFiltersAction = () => async (dispatch) => {
  try {
    dispatch(getPredefinedFiltersRequest());

    const { data } = await getPredefinedFilters();

    dispatch(getPredefinedFiltersSuccess(data));
  } catch (error) {
    dispatch(getPredefinedFiltersFailure(error));
  }
};

export default getPredefinedFiltersAction;
