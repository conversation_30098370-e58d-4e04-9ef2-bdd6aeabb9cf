import { ngaAxios } from 'core/services/HTTPService';
import getPredefinedFilters from './api.service';

jest.mock('core/services/HTTPService');

describe('GetPredefinedFilters', () => {
  test('should get successfully data from an API', async () => {
    const data = '';

    ngaAxios.get.mockImplementationOnce(() => Promise.resolve(data));

    await expect(getPredefinedFilters()).resolves.toEqual(data);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    ngaAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getPredefinedFilters()).rejects.toThrow(errorMessage);
  });
});
