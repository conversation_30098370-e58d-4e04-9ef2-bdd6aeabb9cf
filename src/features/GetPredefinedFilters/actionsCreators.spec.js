import {
  GET_PREDEFINED_FILTERS_REQUEST,
  GET_PREDEFINED_FILTERS_SUCCESS,
  GET_PREDEFINED_FILTERS_FAILURE,
} from './actionTypes';

import {
  getPredefinedFiltersRequest,
  getPredefinedFiltersSuccess,
  getPredefinedFiltersFailure,
} from './actionsCreators';

describe('GetPredefinedFilters: actionCreators', () => {
  test('should create action to getPredefinedFiltersRequest', () => {
    const expectedAction = {
      type: GET_PREDEFINED_FILTERS_REQUEST,
    };

    expect(getPredefinedFiltersRequest()).toEqual(expectedAction);
  });

  test('should create action to getPredefinedFiltersSuccess', () => {
    const data = '';
    const expectedAction = {
      type: GET_PREDEFINED_FILTERS_SUCCESS,
      data,
    };

    expect(getPredefinedFiltersSuccess(data)).toEqual(expectedAction);
  });

  test('should create action to getPredefinedFiltersFailure', () => {
    const error = 'test error';
    const expectedAction = {
      type: GET_PREDEFINED_FILTERS_FAILURE,
      error,
    };

    expect(getPredefinedFiltersFailure(error)).toEqual(expectedAction);
  });
});
