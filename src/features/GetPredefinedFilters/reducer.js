import {
  GET_PREDEFINED_FILTERS_REQUEST,
  GET_PREDEFINED_FILTERS_SUCCESS,
  GET_PREDEFINED_FILTERS_FAILURE,
} from './actionTypes';

const getPredefinedFiltersReducer = (state = { data: [] }, {
  type, data, error,
}) => {
  switch (type) {
    case GET_PREDEFINED_FILTERS_REQUEST:
      return {
        ...state,
        isLoading: true,
      };
    case GET_PREDEFINED_FILTERS_SUCCESS:
      return {
        ...state,
        data,
        isLoading: false,
      };
    case GET_PREDEFINED_FILTERS_FAILURE:
      return {
        ...state,
        isLoading: false,
        error,
      };
    default:
      return state;
  }
};

export default getPredefinedFiltersReducer;
