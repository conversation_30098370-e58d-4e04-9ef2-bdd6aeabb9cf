import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TextField } from '@mui/material';
import PropTypes from 'prop-types';
import Autocomplete from 'shared/Autocomplete';
import getNegotiatorsAction from './GetNegotiators/actions';

const NegotiatorsAutocomplete = ({
  negotiator, onNegotiatorChange, className, label, ...props
}) => {
  const dispatch = useDispatch();
  const negotiators = useSelector((state) => state.negotiators.data);

  const optionKey = 'name';

  const getNegotiatorsList = async () => {
    await dispatch(getNegotiatorsAction());
  };

  useEffect(() => {
    getNegotiatorsList();
  }, []);

  return (
    <Autocomplete
      className={`negotiator-autocomplete ${className}`}
      data-testid="negotiator-autocomplete"
      autoComplete={false}
      selectOnFocus={false}
      options={negotiators}
      getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
      isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
      value={negotiator}
      onChange={onNegotiatorChange}
      renderInput={(params) => (
        <TextField
          {...params}
          className="negotiator-autocomplete__text"
          variant="outlined"
          placeholder="Select Negotiator"
          label={label}
        />
      )}
      optionKey={optionKey}
      {...props}
    />
  );
};

NegotiatorsAutocomplete.propTypes = {
  negotiator: PropTypes.shape({
    id: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
    name: PropTypes.string,
  }),
  onNegotiatorChange: PropTypes.func.isRequired,
  className: PropTypes.string,
  label: PropTypes.string,
};

NegotiatorsAutocomplete.defaultProps = {
  negotiator: null,
  className: '',
  label: null,
};

export default NegotiatorsAutocomplete;
