import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { HTTPService } from 'core/services';
import Autocomplete from 'shared/Autocomplete';
import { useDispatch } from 'react-redux';
import { TextField } from '@mui/material';
import getOperatorsAction from 'features/OperatorsAutocomplete/GetOperators/actions';
import { debounce } from 'lodash';
import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsFilterOptions from 'core/utilities/getOperatorsFilterOptions';

let listOperatorsController = new AbortController();
const compareValue = 3;
const debounceTime = 500;

const OperatorsAutocomplete = ({
  operators, onChangeOperators, className, label, limitTags, ...props
}) => {
  const dispatch = useDispatch();
  const [listOfOperators, setListOfOperators] = React.useState([]);
  const [operatorsInputValue, setOperatorsInputValue] = useState('');
  const optionKey = 'pmn_code';

  const getListOfOperators = debounce(async (params) => {
    HTTPService.cancelRequest(listOperatorsController);

    listOperatorsController = HTTPService.getController();
    const data = await dispatch(getOperatorsAction(listOperatorsController, params)) || [];

    setListOfOperators(data);
  }, debounceTime);

  const changeOperators = async (event, newInputValue) => {
    setOperatorsInputValue(newInputValue);

    if (newInputValue.length >= compareValue) {
      const operatorsListParams = {
        search: newInputValue,
      };
      await getListOfOperators(operatorsListParams);
    }
  };

  const operatorsFilterOptions = getOperatorsFilterOptions();

  return (
    <Autocomplete
      className={`operators-autocomplete ${className}`}
      data-testid="operators-autocomplete"
      multiple
      limitTags={limitTags}
      autoComplete={false}
      selectOnFocus={false}
      options={listOfOperators}
      getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
      isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
      inputValue={operatorsInputValue}
      onInputChange={changeOperators}
      value={operators}
      onChange={onChangeOperators}
      renderInput={(params) => <TextField {...params} placeholder="Enter 3 digits to search" label={label} />}
      optionKey={optionKey}
      filterOptions={operatorsFilterOptions}
      getLabel={(option) => <OperatorsLabel operator={option} />}
      enableSelectAll={false}
      {...props}
    />
  );
};

OperatorsAutocomplete.propTypes = {
  className: PropTypes.string,
  operators: PropTypes.instanceOf(Object),
  onChangeOperators: PropTypes.func.isRequired,
  label: PropTypes.string,
  limitTags: PropTypes.number,
};

OperatorsAutocomplete.defaultProps = {
  className: '',
  operators: null,
  label: null,
  limitTags: 1,
};
export default OperatorsAutocomplete;
