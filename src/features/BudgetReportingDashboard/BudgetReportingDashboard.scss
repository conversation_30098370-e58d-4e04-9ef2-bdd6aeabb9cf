$less-small-mobile-width: 700px;

.budget-reporting-dashboard {
  display: flex;
  flex-direction: column;

  .custom-modal {
    position: absolute !important;
    left: 0 !important;
    bottom: unset !important;
    top: 115px !important;
    right: unset !important;

    &__wrap {
      padding: 32px 0 !important;

      @media (max-width: $small-mobile-width) {
        padding: 20px 0 !important;
      }
    }

    &__title-wrap {
      padding: 0 32px !important;

      @media (max-width: $small-mobile-width) {
        padding: 0 20px !important;
      }
    }

    &__btn-wrap {
      padding: 0 32px !important;

      @media (max-width: $small-mobile-width) {
        padding: 0 20px !important;
      }
    }
  }

  .error-placeholder {
    height: 400px;
    align-items: center;
    justify-content: center;
  }

  .export-traffic-values-button {
    position: absolute;
    top: 64px;
    right: 15px;

    @media (max-width: $less-small-mobile-width) {
      position: static;
      margin: 10px 15px 0 auto;
    }
  }

  .traffic-values-title {
    margin-left: 15px !important;
  }
}
