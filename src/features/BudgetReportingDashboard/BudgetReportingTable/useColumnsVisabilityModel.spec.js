import { renderHook } from '@testing-library/react';
import * as BudgetReportingContext from 'features/BudgetReportingDashboard/BudgetReportingDashboardProvider';
import useColumnsVisibilityModel from './useColumnsVisibilityModel';
import { budgetReportingTableFields, initialColumnsVisibility } from './constants';

describe('BudgetDetails: BudgetValues: BudgetReportingDashboard: BudgetReportingTable: useColumnsVisibilityModel:', () => {
  jest.spyOn(BudgetReportingContext, 'useBudgetReportingContext');

  const testCases = [
    {
      description: 'Default columns visible',
      visibility: initialColumnsVisibility,
      expectedResult: {
        home_operator_pmn: true,
        partner_operator_pmn: true,
        traffic_direction: true,
        service_type: true,
        volume_actual: true,
        tap_charge_gross: true,
        charge_gross: true,
        partner_country: false,
        traffic_month: false,
        traffic_type: false,
        call_destination: false,
        called_country: false,
        is_premium: false,
        traffic_segment_name: false,
        volume_billed: false,
        tap_charge_net: false,
        charge_net: false,
        imsi_count_type: false,
        discount_net: false,
        discount_gross: false,
        tap_rate_net_volume_actual: false,
        tap_rate_gross_volume_actual: false,
        discounted_rate_net_volume_actual: false,
        discounted_rate_gross_volume_actual: false,
        tap_rate_net_volume_billed: false,
        tap_rate_gross_volume_billed: false,
        discounted_rate_net_volume_billed: false,
        discounted_rate_gross_volume_billed: false,
      },
    },
    {
      description: 'All columns visible',
      visibility: Object.values(budgetReportingTableFields).map((item) => item.field),
      expectedResult: {
        home_operator_pmn: true,
        partner_operator_pmn: true,
        partner_country: true,
        traffic_month: true,
        traffic_type: true,
        traffic_direction: true,
        service_type: true,
        call_destination: true,
        called_country: true,
        is_premium: true,
        traffic_segment_name: true,
        volume_actual: true,
        volume_billed: true,
        tap_charge_net: true,
        tap_charge_gross: true,
        charge_net: true,
        charge_gross: true,
        imsi_count_type: true,
        discount_net: true,
        discount_gross: true,
        tap_rate_net_volume_actual: true,
        tap_rate_gross_volume_actual: true,
        discounted_rate_net_volume_actual: true,
        discounted_rate_gross_volume_actual: true,
        tap_rate_net_volume_billed: true,
        tap_rate_gross_volume_billed: true,
        discounted_rate_net_volume_billed: true,
        discounted_rate_gross_volume_billed: true,
      },
    },
    {
      description: 'No columns visible',
      visibility: [],
      expectedResult: {
        home_operator_pmn: false,
        partner_operator_pmn: false,
        partner_country: false,
        traffic_month: false,
        traffic_type: false,
        traffic_direction: false,
        service_type: false,
        call_destination: false,
        called_country: false,
        is_premium: false,
        traffic_segment_name: false,
        volume_actual: false,
        volume_billed: false,
        tap_charge_net: false,
        tap_charge_gross: false,
        charge_net: false,
        charge_gross: false,
        imsi_count_type: false,
        discount_net: false,
        discount_gross: false,
        tap_rate_net_volume_actual: false,
        tap_rate_gross_volume_actual: false,
        discounted_rate_net_volume_actual: false,
        discounted_rate_gross_volume_actual: false,
        tap_rate_net_volume_billed: false,
        tap_rate_gross_volume_billed: false,
        discounted_rate_net_volume_billed: false,
        discounted_rate_gross_volume_billed: false,
      },
    },
  ];

  testCases.forEach(({ description, visibility, expectedResult }) => {
    test(`should be ${description}`, () => {
      BudgetReportingContext.useBudgetReportingContext.mockImplementation(() => ({
        columnsVisibility: visibility,
      }));

      const { result: { current } } = renderHook(() => useColumnsVisibilityModel());

      expect(current).toEqual(expectedResult);
    });
  });
});
