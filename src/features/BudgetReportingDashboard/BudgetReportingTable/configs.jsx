import {
  AiOutlineCheckCircle,
  AiOutlineUnorderedList,
} from 'react-icons/ai';
import React from 'react';
import FormattedNumber from 'shared/FormattedNumber';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { capitalize } from 'lodash';
import getServiceTypeInfo from 'core/utilities/getServiceTypeInfo';
import { getImsiCountTitle } from 'features/BudgetReportingDashboard/utilities';
import { budgetReportingTableFields } from './constants';

export const noDataConfig = (primaryColor) => ({
  icon: <AiOutlineUnorderedList size={40} color={primaryColor} />,
  title: 'No reports in the budget yet.',
  description: '',
});

export const budgetReportingTableConfig = [
  {
    headerName: budgetReportingTableFields.homeOperatorPmn.fieldName,
    field: budgetReportingTableFields.homeOperatorPmn.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.partnerOperatorPmn.fieldName,
    field: budgetReportingTableFields.partnerOperatorPmn.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.partnerCountry.fieldName,
    field: budgetReportingTableFields.partnerCountry.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.trafficMonth.fieldName,
    field: budgetReportingTableFields.trafficMonth.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.trafficType.fieldName,
    field: budgetReportingTableFields.trafficType.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (capitalize(row[budgetReportingTableFields.trafficType.field])),
  },
  {
    headerName: budgetReportingTableFields.trafficDirection.fieldName,
    field: budgetReportingTableFields.trafficDirection.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (capitalize(row[budgetReportingTableFields.trafficDirection.field])),
  },
  {
    headerName: budgetReportingTableFields.serviceType.fieldName,
    field: budgetReportingTableFields.serviceType.field,
    minWidth: 160,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => getServiceTypeInfo(row[budgetReportingTableFields.serviceType.field]),
  },
  {
    headerName: budgetReportingTableFields.callDestination.fieldName,
    field: budgetReportingTableFields.callDestination.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (capitalize(row[budgetReportingTableFields.callDestination.field])),
  },
  {
    headerName: budgetReportingTableFields.calledCountry.fieldName,
    field: budgetReportingTableFields.calledCountry.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.isPremium.fieldName,
    field: budgetReportingTableFields.isPremium.field,
    minWidth: 140,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (row[budgetReportingTableFields.isPremium.field] ? (
      <AiOutlineCheckCircle size={22} color={styles.greenColor500} />) : null
    ),
  },
  {
    headerName: budgetReportingTableFields.trafficSegmentName.fieldName,
    field: budgetReportingTableFields.trafficSegmentName.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
  },
  {
    headerName: budgetReportingTableFields.imsiCountType.fieldName,
    field: budgetReportingTableFields.imsiCountType.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => getImsiCountTitle(row[budgetReportingTableFields.imsiCountType.field]),
  },
  {
    headerName: budgetReportingTableFields.volumeActual.fieldName,
    field: budgetReportingTableFields.volumeActual.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.volumeActual.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.volumeBilled.fieldName,
    field: budgetReportingTableFields.volumeBilled.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.volumeBilled.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapChargeNet.fieldName,
    field: budgetReportingTableFields.tapChargeNet.field,
    minWidth: 170,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapChargeNet.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapChargeGross.fieldName,
    field: budgetReportingTableFields.tapChargeGross.field,
    minWidth: 180,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapChargeGross.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.chargeNet.fieldName,
    field: budgetReportingTableFields.chargeNet.field,
    minWidth: 170,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.chargeNet.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.chargeGross.fieldName,
    field: budgetReportingTableFields.chargeGross.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.chargeGross.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountNet.fieldName,
    field: budgetReportingTableFields.discountNet.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountNet.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountGross.fieldName,
    field: budgetReportingTableFields.discountGross.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountGross.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapRateNetVolumeActual.fieldName,
    field: budgetReportingTableFields.tapRateNetVolumeActual.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapRateNetVolumeActual.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapRateGrossVolumeActual.fieldName,
    field: budgetReportingTableFields.tapRateGrossVolumeActual.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapRateGrossVolumeActual.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountedRateNetVolumeActual.fieldName,
    field: budgetReportingTableFields.discountedRateNetVolumeActual.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountedRateNetVolumeActual.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountedRateGrossVolumeActual.fieldName,
    field: budgetReportingTableFields.discountedRateGrossVolumeActual.field,
    minWidth: 160,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountedRateGrossVolumeActual.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapRateNetVolumeBilled.fieldName,
    field: budgetReportingTableFields.tapRateNetVolumeBilled.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapRateNetVolumeBilled.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.tapRateGrossVolumeBilled.fieldName,
    field: budgetReportingTableFields.tapRateGrossVolumeBilled.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.tapRateGrossVolumeBilled.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountedRateNetVolumeBilled.fieldName,
    field: budgetReportingTableFields.discountedRateNetVolumeBilled.field,
    minWidth: 150,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountedRateNetVolumeBilled.field]}
        isNeedRound
      />
    ),
  },
  {
    headerName: budgetReportingTableFields.discountedRateGrossVolumeBilled.fieldName,
    field: budgetReportingTableFields.discountedRateGrossVolumeBilled.field,
    minWidth: 160,
    sortable: true,
    flex: 1,
    renderCell: ({ row }) => (
      <FormattedNumber
        value={row[budgetReportingTableFields.discountedRateGrossVolumeBilled.field]}
        isNeedRound
      />
    ),
  },
];
