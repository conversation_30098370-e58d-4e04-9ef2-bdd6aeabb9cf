import { MuiTableConstants } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';

export const budgetReportingTableFields = {
  homeOperatorPmn: {
    field: 'home_operator_pmn',
    fieldName: 'Home Operator',
  },
  partnerOperatorPmn: {
    field: 'partner_operator_pmn',
    fieldName: 'Partner Operator',
  },
  partnerCountry: {
    field: 'partner_country',
    fieldName: 'Partner Country',
  },
  trafficMonth: {
    field: 'traffic_month',
    fieldName: 'Traffic Month',
  },
  trafficType: {
    field: 'traffic_type',
    fieldName: 'Traffic Type',
  },
  trafficDirection: {
    field: 'traffic_direction',
    fieldName: 'Traffic Direction',
  },
  serviceType: {
    field: 'service_type',
    fieldName: 'Service Type',
  },
  callDestination: {
    field: 'call_destination',
    fieldName: 'Call Destination',
  },
  calledCountry: {
    field: 'called_country',
    fieldName: 'Called Country',
  },
  isPremium: {
    field: 'is_premium',
    fieldName: 'Premium',
  },
  trafficSegmentName: {
    field: 'traffic_segment_name',
    fieldName: 'Traffic Segment',
  },
  imsiCountType: {
    field: 'imsi_count_type',
    fieldName: 'IMSI Count Type',
  },
  volumeActual: {
    field: 'volume_actual',
    fieldName: 'Volume Actual',
  },
  volumeBilled: {
    field: 'volume_billed',
    fieldName: 'Volume Billable',
  },
  tapChargeNet: {
    field: 'tap_charge_net',
    fieldName: 'TAP Charge Net',
  },
  tapChargeGross: {
    field: 'tap_charge_gross',
    fieldName: 'TAP Charge Gross',
  },
  chargeNet: {
    field: 'charge_net',
    fieldName: 'Charge Net',
  },
  chargeGross: {
    field: 'charge_gross',
    fieldName: 'Charge Gross',
  },
  discountNet: {
    field: 'discount_net',
    fieldName: 'Discount (Net)',
  },
  discountGross: {
    field: 'discount_gross',
    fieldName: 'Discount (Gross)',
  },
  tapRateNetVolumeActual: {
    field: 'tap_rate_net_volume_actual',
    fieldName: 'TAP Rate Net (Volume Actual)',
  },
  tapRateGrossVolumeActual: {
    field: 'tap_rate_gross_volume_actual',
    fieldName: 'TAP Rate Gross (Volume Actual)',
  },
  discountedRateNetVolumeActual: {
    field: 'discounted_rate_net_volume_actual',
    fieldName: 'Discounted Rate Net (Volume Actual)',
  },
  discountedRateGrossVolumeActual: {
    field: 'discounted_rate_gross_volume_actual',
    fieldName: 'Discounted Rate Gross (Volume Actual)',
  },
  tapRateNetVolumeBilled: {
    field: 'tap_rate_net_volume_billed',
    fieldName: 'TAP Rate Net (Volume Billed)',
  },
  tapRateGrossVolumeBilled: {
    field: 'tap_rate_gross_volume_billed',
    fieldName: 'TAP Rate Gross (Volume Billed)',
  },
  discountedRateNetVolumeBilled: {
    field: 'discounted_rate_net_volume_billed',
    fieldName: 'Discounted Rate Net (Volume Billed)',
  },
  discountedRateGrossVolumeBilled: {
    field: 'discounted_rate_gross_volume_billed',
    fieldName: 'Discounted Rate Gross (Volume Billed)',
  },
};

export const initialColumnsVisibility = [
  budgetReportingTableFields.homeOperatorPmn.field,
  budgetReportingTableFields.partnerOperatorPmn.field,
  budgetReportingTableFields.trafficDirection.field,
  budgetReportingTableFields.serviceType.field,
  budgetReportingTableFields.volumeActual.field,
  budgetReportingTableFields.tapChargeGross.field,
  budgetReportingTableFields.chargeGross.field,
];

const { defaultPageSize } = MuiTableConstants;

export const defaultPageSizeOptions = [defaultPageSize, 50, 100];

export const maxTableHeight = '600px';

export const budgetReportingTableModifier = 'budget-reporting-table';

export const imsiCountTypes = [
  { title: 'Data', value: 'DATA' },
  { title: 'No data', value: 'NO_DATA' },
];
