.budget-reporting-configure-columns {
  &__modal {
    &-content {
      display: flex;
      flex-direction: column;
      width: 784px;
      margin-top: 10px;

      @media (max-width: $tablet-width) {
        width: 600px;
      }

      @media (max-width: $mobile-width) {
        width: 300px;
      }

      &-column {
        display: flex;
        flex-direction: column;

        &:not(:nth-child(2)) {
          margin-right: 50px;
        }
      }

      &-title {
        font-size: 14px !important;
        font-weight: 700 !important;
        color: $dark-color-300 !important;
        text-transform: uppercase !important;
      }

      .scrollbar-container {
        display: flex;
        padding: 0 32px;

        @media (max-width: $tablet-width) {
          padding: 0 20px;
          flex-direction: column;
          row-gap: 10px;
          max-height: 300px;
          margin-right: 15px;
        }
      }
    }
  }

  &__reset {
    right: 32px;
    position: absolute !important;
    bottom: 32px;
    padding: 0 21px !important;

    @media (max-width: $tablet-width) {
      position: static !important;
    }

    svg path {
      stroke: $dark-color-300 !important;
    }

    &:hover {
      svg path {
        stroke: $brand-blue-color-500 !important;
        fill: none !important;
      }
    }
  }

  .Mui-disabled {
    background-color: $light-color-200 !important;

    svg {
      color: $white-color !important;
    }
  }

  @media (max-width: $small-mobile-width) {
    .MuiButtonBase-root, .MuiIconButton-root {
      width: 32px !important;
      height: 32px !important;
    }
  }
}
