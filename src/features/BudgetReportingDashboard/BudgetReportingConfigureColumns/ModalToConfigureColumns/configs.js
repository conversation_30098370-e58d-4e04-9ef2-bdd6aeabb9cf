import { budgetReportingTableFields } from 'features/BudgetReportingDashboard/BudgetReportingTable/constants';

export const trafficColumnsConfig = [
  {
    label: budgetReportingTableFields.homeOperatorPmn.fieldName,
    name: budgetReportingTableFields.homeOperatorPmn.field,
  },
  {
    label: budgetReportingTableFields.partnerOperatorPmn.fieldName,
    name: budgetReportingTableFields.partnerOperatorPmn.field,
  },
  {
    label: budgetReportingTableFields.partnerCountry.fieldName,
    name: budgetReportingTableFields.partnerCountry.field,
  },
  {
    label: budgetReportingTableFields.trafficMonth.fieldName,
    name: budgetReportingTableFields.trafficMonth.field,
  },
  {
    label: budgetReportingTableFields.trafficType.fieldName,
    name: budgetReportingTableFields.trafficType.field,
  },
  {
    label: budgetReportingTableFields.trafficDirection.fieldName,
    name: budgetReportingTableFields.trafficDirection.field,
    disabled: true,
  },
  {
    label: budgetReportingTableFields.serviceType.fieldName,
    name: budgetReportingTableFields.serviceType.field,
    disabled: true,
  },
  {
    label: budgetReportingTableFields.callDestination.fieldName,
    name: budgetReportingTableFields.callDestination.field,
  },
  {
    label: budgetReportingTableFields.calledCountry.fieldName,
    name: budgetReportingTableFields.calledCountry.field,
  },
  {
    label: budgetReportingTableFields.isPremium.fieldName,
    name: budgetReportingTableFields.isPremium.field,
  },
  {
    label: budgetReportingTableFields.trafficSegmentName.fieldName,
    name: budgetReportingTableFields.trafficSegmentName.field,
  },
  {
    label: budgetReportingTableFields.imsiCountType.fieldName,
    name: budgetReportingTableFields.imsiCountType.field,
  },
];

export const valueColumnsConfig = [
  {
    label: budgetReportingTableFields.volumeActual.fieldName,
    name: budgetReportingTableFields.volumeActual.field,
  },
  {
    label: budgetReportingTableFields.volumeBilled.fieldName,
    name: budgetReportingTableFields.volumeBilled.field,
  },
  {
    label: budgetReportingTableFields.tapChargeNet.fieldName,
    name: budgetReportingTableFields.tapChargeNet.field,
  },
  {
    label: budgetReportingTableFields.tapChargeGross.fieldName,
    name: budgetReportingTableFields.tapChargeGross.field,
  },
  {
    label: budgetReportingTableFields.chargeNet.fieldName,
    name: budgetReportingTableFields.chargeNet.field,
  },
  {
    label: budgetReportingTableFields.chargeGross.fieldName,
    name: budgetReportingTableFields.chargeGross.field,
    disabled: true,
  },
];

export const calculatedColumnsConfig = [
  {
    label: budgetReportingTableFields.discountNet.fieldName,
    name: budgetReportingTableFields.discountNet.field,
  },
  {
    label: budgetReportingTableFields.discountGross.fieldName,
    name: budgetReportingTableFields.discountGross.field,
  },
  {
    label: budgetReportingTableFields.tapRateNetVolumeActual.fieldName,
    name: budgetReportingTableFields.tapRateNetVolumeActual.field,
  },
  {
    label: budgetReportingTableFields.tapRateGrossVolumeActual.fieldName,
    name: budgetReportingTableFields.tapRateGrossVolumeActual.field,
  },
  {
    label: budgetReportingTableFields.discountedRateNetVolumeActual.fieldName,
    name: budgetReportingTableFields.discountedRateNetVolumeActual.field,
  },
  {
    label: budgetReportingTableFields.discountedRateGrossVolumeActual.fieldName,
    name: budgetReportingTableFields.discountedRateGrossVolumeActual.field,
  },
  {
    label: budgetReportingTableFields.tapRateNetVolumeBilled.fieldName,
    name: budgetReportingTableFields.tapRateNetVolumeBilled.field,
  },
  {
    label: budgetReportingTableFields.tapRateGrossVolumeBilled.fieldName,
    name: budgetReportingTableFields.tapRateGrossVolumeBilled.field,
  },
  {
    label: budgetReportingTableFields.discountedRateNetVolumeBilled.fieldName,
    name: budgetReportingTableFields.discountedRateNetVolumeBilled.field,
  },
  {
    label: budgetReportingTableFields.discountedRateGrossVolumeBilled.fieldName,
    name: budgetReportingTableFields.discountedRateGrossVolumeBilled.field,
  },
];
