import React, { useState } from 'react';
import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';
import { useBudgetReportingContext } from 'features/BudgetReportingDashboard/BudgetReportingDashboardProvider';
import { GrRefresh } from 'react-icons/gr';
import useColumnVisibilityModel from 'features/BudgetReportingDashboard/BudgetReportingTable/useColumnsVisibilityModel';
import {
  modifiers,
  titles,
} from 'features/BudgetReportingDashboard/BudgetReportingConfigureColumns/constants';
import { Button } from '@mui/material';
import { initialColumnsVisibility } from 'features/BudgetReportingDashboard/BudgetReportingTable/constants';

import { getColumnsVisibility } from 'features/BudgetReportingDashboard/utilities';
import PerfectScrollbar from 'react-perfect-scrollbar';
import ColumnsCheckboxList from './ColumnsCheckboxList';
import ColumnsConfigurationSaving from './ColumnsConfigurationSaving';
import {
  calculatedColumnsConfig,
  trafficColumnsConfig,
  valueColumnsConfig,
} from './configs';

const ModalToConfigureColumns = () => {
  const {
    setColumnsVisibility,
    isModalToConfigColumnsOpen,
    closeModalToConfigColumns,
    setColumnsVisibilityChange,
  } = useBudgetReportingContext();

  const columnsVisibility = useColumnVisibilityModel();

  const [columnsCheckState, setColumnsCheckState] = useState(columnsVisibility);

  const confirmColumns = async () => {
    const filteredColumns = Object.keys(columnsCheckState).filter((key) => columnsCheckState[key]);

    await setColumnsVisibilityChange();

    setColumnsVisibility(filteredColumns);

    closeModalToConfigColumns();
  };

  const cancelColumnsConfiguration = () => {
    closeModalToConfigColumns();

    setColumnsCheckState(columnsVisibility);
  };

  const resetColumnsConfiguration = async () => {
    await setColumnsVisibilityChange();

    setColumnsVisibility(initialColumnsVisibility);

    setColumnsCheckState(getColumnsVisibility(initialColumnsVisibility));

    closeModalToConfigColumns();
  };

  const onChangeColumnVisibility = (event) => {
    const { name, checked } = event.target;

    setColumnsCheckState({
      ...columnsCheckState,
      [name]: checked,
    });
  };

  return (
    <CustomModal
      isOpen={isModalToConfigColumnsOpen}
      handleOpen={cancelColumnsConfiguration}
      showButtons
      title={titles.configureColumns}
      container={() => document.querySelector('.budget-reporting-dashboard')}
      BackdropProps={{ invisible: true }}
      onClickConfirm={confirmColumns}
      onClickCancel={cancelColumnsConfiguration}
    >
      <div className={modifiers.modalContent}>
        <PerfectScrollbar>
          <ColumnsCheckboxList
            title={titles.configureTrafficColumns}
            columnsCheckState={columnsCheckState}
            onChange={onChangeColumnVisibility}
            columnsConfig={trafficColumnsConfig}
          />
          <ColumnsCheckboxList
            title={titles.configureValueColumns}
            columnsCheckState={columnsCheckState}
            onChange={onChangeColumnVisibility}
            columnsConfig={valueColumnsConfig}
          />
          <ColumnsCheckboxList
            title={titles.configureCalculatedColumns}
            columnsCheckState={columnsCheckState}
            onChange={onChangeColumnVisibility}
            columnsConfig={calculatedColumnsConfig}
          />
        </PerfectScrollbar>
        <ColumnsConfigurationSaving />
      </div>
      <Button
        className={modifiers.configureColumnsReset}
        startIcon={<GrRefresh />}
        onClick={resetColumnsConfiguration}
      >
        {titles.configureColumnsReset}
      </Button>
    </CustomModal>
  );
};

export default ModalToConfigureColumns;
