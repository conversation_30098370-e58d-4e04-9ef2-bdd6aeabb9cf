.traffic-evolution-dashboard-filters {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 70px;

  @media (max-width: 1800px) {
    gap: 50px !important;
  }

  @media (max-width: 1600px) {
    gap: 20px !important;
  }

  &__checkbox-wrap .MuiFormControlLabel-label {
    font-size: 12px !important;
  }

  @media (max-width: $small-mobile-width) {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 425px) {
    grid-template-columns: 1fr;

    .unit-type__radio-group {
      margin: 0 !important;
    }

    .unit-direction-types-autocomplete, .service-type-autocomplete,
    .volume-type-autocomplete, .charge-type-autocomplete,
    .charge-type-autocomplete__wrap, .currencies-autocomplete {
      width: 100%;
    }
  }

  &__charge-wrap {
    display: flex;
    align-items: center;
    gap: 70px;

    @media (max-width: 1800px) {
      gap: 50px !important;
    }

    @media (max-width: 1600px) {
      gap: 20px !important;
    }

    @media (max-width: $small-mobile-width) {
      flex-direction: column;
      align-items: flex-start;
      row-gap: 20px;
    }

    @media (max-width: 425px) {
      flex-direction: row;
      align-items: flex-end;

      .currencies-autocomplete {
        margin-top: 20px;
      }
    }
  }

  .charge-type-autocomplete__wrap {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 10px;
  }

  .volume-type-autocomplete__wrap {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 10px;
  }
}
