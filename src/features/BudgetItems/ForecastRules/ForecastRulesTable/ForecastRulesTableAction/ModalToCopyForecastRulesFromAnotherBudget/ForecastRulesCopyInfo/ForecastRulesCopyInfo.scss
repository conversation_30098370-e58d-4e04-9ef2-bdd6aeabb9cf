.forecast-rules-copy-info {
  display: flex;
  gap: 10px;
  align-items: center;
  order: 1;

  @media (max-width: $tablet-width) {
    order: -1;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background-color: $orange-color-50;
    flex-shrink: 0;

    & svg, & svg path {
      color: $orange-color-500 !important;
      stroke: $orange-color-500 !important;
    }
  }

  &__title {
    max-width: 390px;
  }
}
