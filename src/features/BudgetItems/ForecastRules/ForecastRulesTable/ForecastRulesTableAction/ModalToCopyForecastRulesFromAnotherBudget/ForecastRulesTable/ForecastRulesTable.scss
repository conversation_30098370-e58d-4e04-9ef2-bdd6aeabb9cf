@import "src/pages/BudgetDetails/BudgetItems/variables";

.forecast-rules-table-in-modal {
  &__cell {
    color: $dark-color-500;
  }

  &__wrap {
    display: flex;
    flex-direction: column;

    .MuiDataGrid-cell {
      min-height: 47px !important;
    }

    .interactions {
      &__actions-selections {
        @media (max-width: $small-mobile-width) {
          margin-left: 0 !important;
        }
      }

      @media (max-width: $small-mobile-width) {
        flex-direction: column;
        gap: 20px;
      }

      .search {
        @media (max-width: $small-mobile-width) {
          width: 100%;
        }
      }
    }

    .MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows {
      @media (max-width: $small-mobile-width) {
        display: none !important;
      }
    }

    .MuiDataGrid-root .MuiTablePagination-toolbar .MuiInputBase-root {
      @media (max-width: $small-mobile-width) {
        display: none !important;
      }
    }
  }
}
