$extra-small-mobile: 375px;

.partner-country-operator-shares-table {
  th, td {
    border-width: 0;
    border-color: $white-color !important;
    background-color: $white-color !important;
    font-size: 14px !important;
    color: $dark-color-500 !important;
  }

  th {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 5px !important;
  }

  td {
    height: 40px;
    padding: 0 5px !important;
    white-space: nowrap !important;

    .marker {
      position: relative;
      top: -2px;
      display: inline-flex;
      margin-right: 10px;
    }

    [data-name="formatted-number"] {
      width: 100px;
      height: 30px;

      input {
        height: 30px;
      }
    }
  }

  .no-data__wrap {
    height: 380px;
  }

  &__wrapper {
    position: relative;
    height: calc(100% - 45px) !important;
    overflow: auto;
    margin-bottom: 15px !important;

    @media (max-width: $tablet-width) {
      height: calc(100% - 90px) !important;
    }

    @media (max-width: $mobile-width) {
      max-height: 400px !important;
      height: auto;
    }

    @media (max-width: $extra-small-mobile) {
      max-height: 260px !important;
    }
  }

  &__block {
    position: relative;
  }
}
