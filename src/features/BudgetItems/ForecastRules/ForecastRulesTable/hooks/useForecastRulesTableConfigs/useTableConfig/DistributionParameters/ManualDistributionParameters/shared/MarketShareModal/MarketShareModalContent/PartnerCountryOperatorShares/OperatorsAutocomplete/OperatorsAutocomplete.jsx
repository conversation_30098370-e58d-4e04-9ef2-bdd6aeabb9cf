import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

import { useDispatch, useSelector } from 'react-redux';
import { difference } from 'lodash';
import { Chip, TextField } from '@mui/material';

import { HTTPService } from 'core/services';
import Autocomplete from 'shared/Autocomplete';
import getOperatorsAction from 'features/OperatorsAutocomplete/GetOperators/actions';

import {
  useMarketShareModalContext,
} from 'features/BudgetItems/ForecastRules/ForecastRulesTable/hooks/useForecastRulesTableConfigs/useTableConfig/DistributionParameters/ManualDistributionParameters/shared/MarketShareModal/MarketShareModalContent/MarketShareModalContext';
import {
  partnerOperatorSharesKey,
} from 'features/BudgetItems/ForecastRules/ForecastRulesTable/hooks/useForecastRulesTableConfigs/useTableConfig/DistributionParameters/ManualDistributionParameters/shared/MarketShareModal/MarketShareModalContent/hooks/useModalData/constants';

import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsFilterOptions
  from 'core/utilities/getOperatorsFilterOptions';

import useDeviceResolution from 'core/hooks/useDeviceResolution';
import {
  operatorsAutocompleteModifier,
  limitTags,
  optionKey,
  operatorsAutocompleteWrapModifier, limitTagsMobile,
} from './constants';
import OperatorsAutocompleteActions from './OperatorsAutocompleteActions';

let listOperatorsController = new AbortController();

const OperatorsAutocomplete = ({
  selectedCountryOperatorsQuantity,
}) => {
  const {
    selectedCountry,
    modalData,
    removeCountryOperatorFromModalData,
    addCountryOperatorToModalData,
    readonly,
    isCountrySelectionVisible,
  } = useMarketShareModalContext();

  const dispatch = useDispatch();

  const listOfOperators = useSelector((state) => state.operators.data) || [];

  const { isMobile } = useDeviceResolution();
  const [selectedOperators, setSelectedOperators] = useState([]);

  const getListOfOperatorsByCountry = () => {
    const params = {
      country_codes: [selectedCountry],
    };

    HTTPService.cancelRequest(listOperatorsController);

    listOperatorsController = HTTPService.getController();
    dispatch(getOperatorsAction(listOperatorsController, params));
  };

  const setSelectedOperatorsByCountry = () => {
    const newSelectedOperators = Object
      .values(modalData[selectedCountry]?.[partnerOperatorSharesKey])
      .map((item) => item.operator);

    setSelectedOperators(newSelectedOperators);
  };

  const onChangeOperators = (e, nextValue) => {
    const prevValues = Object
      .values(modalData[selectedCountry]?.[partnerOperatorSharesKey])
      .map((item) => item.operator);

    const isAdding = nextValue.length > prevValues.length;
    const arraysForDiff = isAdding ? {
      first: nextValue,
      second: prevValues,
    } : {
      first: prevValues,
      second: nextValue,
    };
    const action = isAdding ? addCountryOperatorToModalData : removeCountryOperatorFromModalData;
    const diffPmnCode = difference(arraysForDiff.first, arraysForDiff.second)[0]?.pmn_code;

    action(diffPmnCode);
  };

  const operatorsFilterOptions = getOperatorsFilterOptions();

  useEffect(() => {
    getListOfOperatorsByCountry();

    setSelectedOperatorsByCountry();
  }, [selectedCountry]);

  useEffect(() => {
    setSelectedOperatorsByCountry();
  }, [selectedCountryOperatorsQuantity]);

  return (
    <div className={operatorsAutocompleteWrapModifier}>
      <Autocomplete
        disabled={isCountrySelectionVisible}
        className={operatorsAutocompleteModifier}
        data-testid={operatorsAutocompleteModifier}
        multiple
        limitTags={isMobile ? limitTagsMobile : limitTags}
        autoComplete={false}
        selectOnFocus={false}
        options={listOfOperators}
        getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
        isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
        value={selectedOperators}
        onChange={onChangeOperators}
        renderInput={(params) => <TextField {...params} placeholder="Search" label="Select operators" />}
        optionKey={optionKey}
        disableClearable
        readOnly={readonly}
        filterOptions={operatorsFilterOptions}
        getLabel={(option) => <OperatorsLabel operator={option} />}
        enableSelectAll={false}
        {...(isMobile && {
          renderTags: (value, getTagProps) => {
            const numTags = value.length;
            return (
              <>
                {value.slice(0, limitTagsMobile).map((option, index) => (
                  <Chip
                    {...getTagProps({ index })}
                    key={option[optionKey]}
                    label={option[optionKey]}
                  />
                ))}
                {numTags > limitTags && ` +${numTags - limitTags}`}
              </>
            );
          },
        })}
      />
      {!readonly
      && (
      <OperatorsAutocompleteActions
        selectedCountryOperatorsQuantity={selectedCountryOperatorsQuantity}
      />
      )}
    </div>
  );
};

OperatorsAutocomplete.propTypes = {
  selectedCountryOperatorsQuantity: PropTypes.number,
};

OperatorsAutocomplete.defaultProps = {
  selectedCountryOperatorsQuantity: 0,
};

export default OperatorsAutocomplete;
