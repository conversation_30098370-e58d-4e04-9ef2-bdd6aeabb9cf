import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import { TextField } from '@mui/material';

import Autocomplete from 'shared/Autocomplete';

const CountriesAutocomplete = ({
  country, onCountryChange, className, label, limitTags, disabled,
}) => {
  const countries = useSelector((state) => state.countries.data);
  const optionKey = 'name';

  return (
    <Autocomplete
      className={`countries-autocomplete ${className}`}
      data-testid="countries-autocomplete"
      autoComplete={false}
      selectOnFocus={false}
      multiple
      limitTags={limitTags}
      options={countries}
      getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
      isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
      value={country}
      onChange={onCountryChange}
      renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label={label} />}
      optionKey={optionKey}
      disabled={disabled}
    />
  );
};

CountriesAutocomplete.propTypes = {
  onCountryChange: PropTypes.func,
  country: PropTypes.instanceOf(Object),
  className: PropTypes.string,
  label: PropTypes.string,
  limitTags: PropTypes.number,
  disabled: PropTypes.bool,
};

CountriesAutocomplete.defaultProps = {
  onCountryChange: () => {},
  country: null,
  className: '',
  label: null,
  limitTags: 1,
  disabled: false,
};

export default CountriesAutocomplete;
