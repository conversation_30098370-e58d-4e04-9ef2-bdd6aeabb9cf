# Getting Started with nga-ui 

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app) and was configured with [CRACO](https://github.com/dilanx/craco).\
Before installing dependencies, make sure you have Node.js v22.

Project uses two private gitlab packages:
- [nv2-pkg-js-shared-components](https://gitlabnv2.flyaps.com/nv2/pkg/js/nv2-pkg-js-shared-components)
- [nv2-pkg-js-theme](https://gitlabnv2.flyaps.com/nv2/pkg/js/nv2-pkg-js-theme)

For authorization in development mode need to run the application [nv2-core-auth-keycloak-ui](https://gitlabnv2.flyaps.com/nv2/core/nv2-core-auth-keycloak-ui).\
See more [details](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Working-with-protected-API-locally).

### Runtime|env variables

1. To work with nv2-core-auth-keycloak-ui need .env with url variables. See [example.env](example.env)
2. To get API URLs and other environment-specific configurations, the application makes a request to `/settings/config.json`.\
   For example:\
   `{`\
    `apiUrl: "https://example-api"`\
    `keycloakLogoutUrl: ''` _read more about this variable in [Single Loogout](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Single-Logout-(SLO)) and [Sign Out](https://nextgenclearing.atlassian.net/wiki/spaces/V2D/pages/211714049/OAuth2+Proxy#Sign-out)_\
    `}`

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3006](http://localhost:3006) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm run lint`

[ESLint](https://github.com/eslint/eslint) statically analyzes .js and .jsx code to quickly find problems.\
Uses [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)

### `npm run stylelint`

[Stylelint](https://github.com/stylelint/stylelint) that helps avoid errors and enforce conventions in styles\
Uses [standard shareable SCSS config for Stylelint](https://www.npmjs.com/package/stylelint-config-standard-scss)

### `npm test`

Launches the [Jest](https://jestjs.io/) in the interactive watch mode.

### `npm test:coverage`

Gets a code coverage report.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)

## IOTRatesTable Component Documentation

### Overview

The IOTRatesTable component is a comprehensive data table that displays IOT (Internet of Things) rates with advanced filtering, sorting, and pagination capabilities. It's built using Material-UI's DataGrid component and integrates with Redux for state management.

### Architecture

The IOTRatesTable follows a modular architecture with clear separation of concerns:

```
src/pages/IOTRatesList/
├── IOTRatesTable/
│   ├── IOTRatesTable.jsx              # Main table component
│   ├── IOTRatesTableProvider.jsx      # Provider with MuiTable context
│   ├── configs.js                     # Table column configurations
│   ├── constants.js                   # Constants and field mappings
│   └── IOTRatesTable.scss            # Component styles
├── IOTRatesFilters/                   # Filtering functionality
├── IOTRatesContext.jsx                # React context for state management
├── IOTRatesListContainer.jsx          # Main container component
└── GetIOTRates/                       # API integration
    ├── actions.js                     # Redux actions
    ├── reducer.js                     # Redux reducer
    ├── api.service.js                 # API service
    └── apiUrls.js                     # API endpoints
```

### Component Hierarchy

```
IOTRatesListContainer
├── IOTRatesProvider (Context)
│   └── IOTRatesTableProvider (MuiTable Context)
│       └── IOTRatesTable
│           ├── MuiTable (from @nv2/nv2-pkg-js-shared-components)
│           └── IOTRatesFilters
│               └── IOTRatesFiltersModalContent
│                   ├── Filters
│                   └── Actions
```

### Key Features

#### 1. Data Display
- **Columns**: ID, Home Operator, Partner Operator, Direction, Service Type, Called Countries, Is Premium, Start Date, End Date, Type, Currency, Rate
- **Custom Renderers**: Service types with color-coded markers, formatted operators, limited text with tooltips
- **Sorting**: All columns are sortable
- **Pagination**: Configurable page sizes (10, 50, 100)

#### 2. Filtering System
- **Home/Partner Operators**: Multi-select autocomplete with operator names and PMN codes
- **Traffic Direction**: Inbound/Outbound selection
- **Service Types**: Multi-select from predefined service type configurations
- **Called Countries**: Multi-select country filter
- **Premium Status**: Radio group (All/Yes/No/-)
- **Date Ranges**: Start and end date filtering
- **Rate Types**: Standard/EU Regulated/Discounted
- **Currencies**: Multi-select currency filter

#### 3. Service Type Configuration
The component uses `serviceTypeConfig` from `core/contstants` which includes:
- **VOICE_MO**: Voice Mobile Originated
- **VOICE_MT**: Voice Mobile Terminated
- **SMS_MO**: SMS Mobile Originated
- **SMS_MT**: SMS Mobile Terminated
- **DATA**: Data services
- **VOLTE**: Voice over LTE
- **ACCESS_FEE**: Access fee services

Each service type has associated colors for visual distinction in the UI.

### API Integration

#### Endpoints
- **GET /iot_rates/**: Fetches IOT rates data with pagination and filtering
- **GET /iot_rates/filters**: Fetches available filter options

#### Request Parameters
```javascript
{
  page: number,                    // Page number (1-based)
  page_size: number,              // Items per page
  search: string,                 // Search query
  sort_field: string,             // Sort field with direction
  home_operators: array,          // Home operator IDs
  partner_operators: array,       // Partner operator IDs
  traffic_directions: array,      // Direction values
  service_types: array,           // Service type values
  called_countries: array,        // Country IDs
  is_premium: boolean|string,     // Premium status
  start_date_min: string,         // Start date filter (min)
  start_date_max: string,         // Start date filter (max)
  end_date_min: string,           // End date filter (min)
  end_date_max: string,           // End date filter (max)
  types: array,                   // Rate types
  currency_codes: array           // Currency codes
}
```

#### Response Structure
```javascript
{
  results: [
    {
      id: number,
      home_operator: {
        id: number,
        pmn_code: string,
        name: string
      },
      partner_operator: {
        id: number,
        pmn_code: string,
        name: string
      },
      traffic_direction: string,
      service_type: string,
      called_countries: [
        {
          id: number,
          code: string,
          name: string
        }
      ],
      is_premium: boolean|null,
      start_date: string,
      end_date: string,
      type: string,
      currency_code: string,
      value: number
    }
  ],
  count: number
}
```

### Redux State Management

#### State Structure
```javascript
// IOT Rates State
state.iotRates = {
  data: {
    results: [],              // Array of IOT rate objects
    count: 0                  // Total count for pagination
  },
  isLoading: boolean,         // Loading state
  error: object|null          // Error information
}

// IOT Rates Filters State
state.iotRatesFilters = {
  data: {},                   // Available filter options
  isLoading: boolean,         // Loading state
  error: object|null          // Error information
}
```

#### Actions
- **getIOTRatesRequest**: Initiates data fetching
- **getIOTRatesSuccess**: Handles successful data retrieval
- **getIOTRatesFailure**: Handles API errors
- **getIOTRatesCanceled**: Handles request cancellation

### Component Configuration

#### Table Columns Configuration (`configs.js`)
Each column is configured with:
- **headerName**: Display name
- **field**: Data field mapping
- **minWidth**: Minimum column width
- **flex**: Flexible width ratio
- **sortable**: Enable/disable sorting
- **renderCell**: Custom cell renderer function

#### Constants (`constants.js`)
- **iotRatesFields**: Field name mappings
- **directionOptions**: Traffic direction options
- **isPremiumOptions**: Premium status options
- **typeOptions**: Rate type options
- **defaultPageSizeOptions**: Available page sizes

### Styling

The component uses SCSS with BEM methodology:
- **Base class**: `.iot-rates-table`
- **Modifiers**: `__wrap`, `__row`, `__cell`, `__actions`
- **Responsive design**: Mobile-first approach with breakpoints
- **Theme integration**: Uses Material-UI theme colors

### Context Management

#### IOTRatesContext
Provides centralized state management for:
- **iotRatesFilters**: Current filter values
- **setIotRatesFilters**: Filter update function
- **dispatchGetIOTRatesFiltersAction**: Filter options fetching

#### MuiTableContext
Handles table-specific state:
- **pagination**: Current page and page size
- **searchValue**: Search input value
- **sort**: Current sort configuration

### Usage Example

```jsx
import IOTRatesListContainer from 'pages/IOTRatesList/IOTRatesListContainer';

// Basic usage
function App() {
  return (
    <div>
      <IOTRatesListContainer />
    </div>
  );
}

// The component is self-contained and handles all state management internally
```

### Performance Optimizations

1. **Request Cancellation**: Uses AbortController to cancel pending requests
2. **Debounced Search**: Search input is debounced to reduce API calls
3. **Memoized Context**: Context values are memoized to prevent unnecessary re-renders
4. **Virtual Scrolling**: Disabled for better performance with current data size
5. **Lazy Loading**: Components are loaded on demand

### Testing Considerations

#### Unit Testing
- Test individual column renderers
- Test filter logic and transformations
- Test Redux actions and reducers
- Mock API responses for consistent testing

#### Integration Testing
- Test complete data flow from API to UI
- Test filter interactions and state updates
- Test pagination and sorting functionality
- Test error handling scenarios

#### E2E Testing
- Test user workflows (filtering, sorting, pagination)
- Test responsive behavior across devices
- Test accessibility compliance
- Test performance under load

### Troubleshooting

#### Common Issues
1. **Service Type Display**: Ensure `serviceTypeConfig` is properly imported from `core/contstants`
2. **Filter Not Working**: Check filter field mappings in constants
3. **API Errors**: Verify endpoint URLs and request parameters
4. **Performance Issues**: Check for unnecessary re-renders and optimize context usage

#### Debug Tips
- Use Redux DevTools to monitor state changes
- Check network tab for API request/response details
- Use React DevTools to inspect component hierarchy
- Enable console logging for filter transformations

### Dependencies

#### Core Dependencies
- **React**: ^18.x
- **@mui/material**: Material-UI components
- **@reduxjs/toolkit**: Redux state management
- **react-redux**: React-Redux bindings

#### Internal Dependencies
- **@nv2/nv2-pkg-js-shared-components**: Shared UI components
- **core/services**: HTTP service utilities
- **core/utilities**: Helper functions
- **shared/**: Reusable components

### Future Enhancements

1. **Export Functionality**: Add CSV/Excel export capabilities
2. **Advanced Filtering**: Implement range filters for numeric fields
3. **Bulk Operations**: Add multi-select and bulk actions
4. **Real-time Updates**: Implement WebSocket for live data updates
5. **Caching**: Add intelligent caching for frequently accessed data
```
